# CLAUDE.md - Project-Specific Instructions

## PURPOSEFUL BRITTLENESS DURING LLM DEV

- dont use defaults
- don't do exception handling
- don't mock apis
- don't add fallbacks
- etc
- this is important

## VIRTUAL ENVIRONMENT CONVENTIONS

- use .venv instead of venv for virtual environments

## SECURITY AND CONFIGURATION

- always put keys etc in .env file and access via dotenv
- config params should be stored globally either in CFG singleton in run.py or run_config.py

## DEVELOPMENT WORKFLOW

- commit regularly during operations for easy save points `<feat>` `<fix>` tags etc. use sub-tasks (parllel) for this to prevent blocking the main claude code thread.
- always update claude.md using subtasks to not block the main claude code thread.
- always run scripts and existing tests to prevent regression errors.
- after completing tasks, suggest short list of regression tests to add to ./tests to prevent regression errors going forward. Each suggestion should be a single line "if [function] does/doesn't x then broken" for readability ease. When then writing those tests, those single line descriptions should be added to that tests success/fail print.
- Always run scripts after editing to check they work

## REPO DOCUMENTATION

- maintain a comprehensive spec section that serves as a complete instruction set for rebuilding the repo from scratch
- update claude.md incrementally to track design choices and repository structure
- ensure the spec can be used as a standalone guide for repository reconstruction

## FILE NAMING CONVENTIONS

- if file isn't working it should have **_WIP.** suffix, changed back once confirmed working and tests working.

## PROJECT STRUCTURE AND SCRIPT CONVENTIONS

- use ./src/appname and ./tests, ./.venv, ./.claude, ./CLAUDE.md, ./scripts and ./run.py file structure
- launch scripts should be named run_***.sh
- include a justfile in root to provide a minimalistic cheatsheet for running scripts, main app, tests, setup etc. for other developers

## CODE REFERENCE AND RESEARCH

- Always check mcp context7 for code snippets for APIs if available (use minimal keywords, try several things in parallel as it filters quite aggressively)
- Always check online for working code snippets to avoid hallucinations

## RUN SCRIPT CONVENTIONS

- create run.sh with a standard template that includes shebang, error handling, and executable permissions
- ensure run.sh can be used to launch the main application, run tests, or perform setup tasks

## MODULE INITIALIZATION

- add blank __init__.py to all app dirs, /scripts etc to make them modules. If using uv for deps, register modules in pyproject.toml.

## TASK MANAGEMENT AND DEVELOPMENT

- Always explicitly plan what you can do as sub-task agents to maximise speed and parellisation of your actions.

## IMPORT AND MODULE MANAGEMENT

- never use the os.path hack to fix import issues. do it properly, check pyproject.toml or module structure.

## CODE CLARITY AND READABILITY

- Explicitly State elif when multiple states, don't leave reader guessing. if x : abc  elif y: xyz. Not just else xyz.

## GIT COMMIT CONVENTIONS

- please sign git commit messages -Claude for clarity

## JUSTFILE DOCUMENTATION

- Provide a concise, one-line description of the justfile's purpose and ideal formatting to serve as a quick reference for developers

## TABLE FORMATTING

- always include rich table divider lines

## TASK TRACKING

- Maintain a zTasks.md file as a visual tracker for tasks
- Task checklist should be at the top of zTasks.md, in chronological order
- New tasks go at the bottom of the checklist
- Complete tasks should be checked off but not removed
- Include verbatim copies of tasks and requirements
- Organize the rest of the file by feature/branch for readability
- Indent sub-tasks to show hierarchy
- Macro tasks should be markdown hyperlinked to the original prompt
- zTasks.md should be in the root directory and gitignored

## TASK LIST MANAGEMENT

- Don't mock anything not explicitly told to mock
- Maintain a list of tasks at the end of the task list
- Prepend personal tasks with af_** prefix
- If a task has a dependency on the user, move it after the relevant "af_" task
- Pause and wait for user input if encountering a task with user dependency

## PERSONAL DETAILS

- 'af' is my name. Alex Foster.

## DEBUGGING AND TESTING

- how do you know it's working? add minimal test cases that print or log core functionality to verify basic system behavior
- ALWAYS RUN AND TEST YOUR CODE BEFORE COMPLETING. how else do you know it works?

## CLI TOOLS

- cc = claudecode

## PARSING AND DATA EXTRACTION

- I very rarely use programatic parsers. Almost always we will use LLMs w structured outputs for this.