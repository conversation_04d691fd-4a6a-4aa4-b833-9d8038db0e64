name: Sourcery Code Review

on:
  pull_request:
    types: [opened, synchronize]

jobs:
  sourcery:
    runs-on: ubuntu-latest
    name: Sourcery Code Review
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Install Sourcery
        run: pip install sourcery-cli

      - name: Run Sourcery Review
        env:
          SOURCERY_TOKEN: ${{ secrets.SOURCERY_TOKEN }}
        run: |
          # Run Sourcery on changed files only
          # This is non-verbose - only shows actual issues/suggestions
          sourcery review \
            --diff "origin/${{ github.base_ref }}" \
            --check \
            --no-summary

      # Optional: Post results as PR comment
      # Uncomment if you want inline PR comments
      # - name: Sourcery PR Comments
      #   uses: sourcery-ai/sourcery-action@v1
      #   with:
      #     token: ${{ secrets.SOURCERY_TOKEN }}