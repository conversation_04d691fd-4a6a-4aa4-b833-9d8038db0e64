# Generated from pyproject.toml - do not edit directly
#
# This file is autogenerated by pip-compile with Python 3.12
# by the following command:
#
#    pip-compile --no-emit-index-url --output-file=requirements.txt pyproject.toml
#
aiofiles==24.1.0
    # via pfc (pyproject.toml)
annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via
    #   httpx
    #   mcp
    #   openai
    #   sse-starlette
    #   starlette
attrs==25.3.0
    # via
    #   jsonschema
    #   referencing
certifi==2025.7.9
    # via
    #   httpcore
    #   httpx
    #   requests
charset-normalizer==3.4.2
    # via requests
click==8.2.1
    # via
    #   typer
    #   uvicorn
colorama==0.4.6
    # via griffe
distro==1.9.0
    # via openai
fastapi==0.116.0
    # via pfc (pyproject.toml)
griffe==1.7.3
    # via openai-agents
h11==0.16.0
    # via
    #   httpcore
    #   uvicorn
httpcore==1.0.9
    # via httpx
httpx==0.28.1
    # via
    #   mcp
    #   openai
httpx-sse==0.4.1
    # via mcp
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
jiter==0.10.0
    # via openai
jsonschema==4.24.0
    # via mcp
jsonschema-specifications==2025.4.1
    # via jsonschema
markdown-it-py==3.0.0
    # via rich
mcp[cli]==1.10.1
    # via
    #   openai-agents
    #   pfc (pyproject.toml)
mdurl==0.1.2
    # via markdown-it-py
openai==1.93.3
    # via openai-agents
openai-agents==0.1.0
    # via pfc (pyproject.toml)
pydantic==2.11.7
    # via
    #   fastapi
    #   mcp
    #   openai
    #   openai-agents
    #   pfc (pyproject.toml)
    #   pydantic-settings
pydantic-core==2.33.2
    # via pydantic
pydantic-settings==2.10.1
    # via mcp
pygments==2.19.2
    # via rich
python-dotenv==1.1.1
    # via
    #   mcp
    #   pydantic-settings
python-multipart==0.0.20
    # via mcp
rapidfuzz==3.13.0
    # via pfc (pyproject.toml)
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
requests==2.32.4
    # via openai-agents
rich==14.0.0
    # via typer
rpds-py==0.26.0
    # via
    #   jsonschema
    #   referencing
shellingham==1.5.4
    # via typer
sniffio==1.3.1
    # via
    #   anyio
    #   openai
sse-starlette==2.4.1
    # via mcp
starlette==0.46.2
    # via
    #   fastapi
    #   mcp
tqdm==4.67.1
    # via openai
typer==0.16.0
    # via mcp
types-requests==2.32.4.20250611
    # via openai-agents
typing-extensions==4.14.1
    # via
    #   anyio
    #   fastapi
    #   openai
    #   openai-agents
    #   pydantic
    #   pydantic-core
    #   referencing
    #   typer
    #   typing-inspection
typing-inspection==0.4.1
    # via
    #   pydantic
    #   pydantic-settings
urllib3==2.5.0
    # via
    #   requests
    #   types-requests
uvicorn==0.35.0
    # via
    #   mcp
    #   pfc (pyproject.toml)
# Run: just compile-deps
