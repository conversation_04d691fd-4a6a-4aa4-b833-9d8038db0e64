{
    "recommendations": [
        // AI and Coding Assistants
        "anthropic.claude-code",
        "github.copilot",
        "github.copilot-chat",
        "openai.chatgpt",
        "augment.vscode-augment",
        "rooveterinaryinc.roo-cline",
        
        // Python Development
        "ms-python.python",
        // "ms-python.vscode-pylance",
        "ms-python.debugpy",
        "charliermarsh.ruff",
        "ms-python.black-formatter",
        "ms-python.isort",
        "pydantic.logfire",
        
        // Jupyter and Data Science
        "ms-toolsai.jupyter",
        "ms-toolsai.jupyter-keymap",
        "ms-toolsai.jupyter-renderers",
        // "ms-toolsai.vscode-jupyter-cell-tags",
        // "ms-toolsai.vscode-jupyter-slideshow",
        
        // File Viewers and Editors
        "cweijan.vscode-office",
        "dvirtz.parquet-viewer",
        "grapecity.gc-excelviewer",
        "mechatroner.rainbow-csv",
        "tomoki1207.pdf",
        "telesoho.vscode-markdown-paste-image",
        "zaaack.markdown-editor",
        
        // JSON/YAML/TOML
        "blueglassblock.better-json5",
        "hilleer.yaml-plus-json",
        "imgildev.vscode-json-flow",
        "redhat.vscode-yaml",
        "tamasfe.even-better-toml",
        "zardoy.fix-all-json",
        
        // Themes and Icons
        // "catppuccin.catppuccin-vsc",
        // "catppuccin.catppuccin-vsc-icons",
        // "alexdauenhauer.catppuccin-noctis",
        // "dooez.alt-catppuccin-vsc",
        // "thang-nm.catppuccin-perfect-icons",
        // "azemoh.one-monokai",
        // "monokai.theme-monokai-pro-vscode",
        
        // Development Tools
        "arjun.swagger-viewer",
        "formulahendry.code-runner",
        "ms-azuretools.vscode-containers",
        "ms-vscode-remote.remote-containers",
        
        // Platform Specific
        "idleberg.applescript"
    ]
}