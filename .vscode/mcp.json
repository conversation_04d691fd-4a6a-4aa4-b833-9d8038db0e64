{"$schema": "https://modelcontextprotocol.io/schemas/v1.0/mcp.schema.json", "comment": "UNTESTED LIKELY NOT CORRECT. MCP server configuration for PFC project - shareable with team", "inputs": [{"id": "openai_api_key", "description": "OpenAI API Key for AI pipeline", "type": "promptString", "password": true}, {"id": "anthropic_api_key", "description": "Anthropic API Key for Claude models", "type": "promptString", "password": true}], "servers": {"filesystem": {"comment": "Local filesystem access for the project", "type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem"], "env": {}}, "github": {"comment": "GitHub integration for repository management", "type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "${input:github_pat}"}, "disabled": true}, "memory": {"comment": "Memory server for context persistence", "type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {}}, "context7": {"comment": "Real-time, version-aware documentation for libraries - append 'use context7' to prompts", "type": "stdio", "command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "env": {}}}}