# dir tree 2

from https://chatgpt.com/c/686da621-00e0-8003-adf7-3a496d40b745?model=o3

Below is the **fully-annotated “Duplo-alphabetised” repo skeleton** that:

* mirrors every non-parallelogram box in the flow-chart,
* adds `iteration_machine_1/2/3` at the exact stages you asked,
* alphabetises **every** folder/file (`aa_`, `ba_`, …) so IDE trees read like the diagram,
* installs a **#CLAUDE.md** explainer in every importable package,
* sticks to **pip + uv** (no Poetry), one  **top-level `pyproject.toml`** ,
* leaves room for adapters, UI harnesses, notebooks, and scratch work.

Copy-paste into `tree` and you’ll get colourised folders; comments start with `#` lines and are *not* part of the filenames.

```text
.                                         # — repo root — -------------------------------------------------
├── pyproject.toml                        # single file for deps/ruff/mypy; uv will read this (no Poetry)
├── README.md                             # quick-start, Mermaid flow, naming rationale
├── .gitignore                            # ignore .tmp/, __pycache__, .venv, *.log …
├── notebooks/                            # exploratory Jupyter work (never imported)
│   └── 2025-07-pipeline-scratch.ipynb
├── .tmp/                                 # throw-away artefacts; always git-ignored
└── src/                                  # the ONLY path added to PYTHONPATH (via “src/ pattern”)
    └── p/                                # root *runtime* package (short prefix), holds all stages
        ├── __init__.py                   # keeps p/ importable
        ├── #CLAUDE.md                    # describes the whole project in 8-10 lines
        │
        │  # ── AA: cross-cutting core (models, pipeline, utils) ───────────────────────────────────
        ├── aa_core/
        │   ├── __init__.py
        │   ├── #CLAUDE.md                # explains why core exists & how to extend
        │   ├── models.py                 # Pydantic types shared by ≥2 stages
        │   ├── prompts.py                # PROMPTS singleton (micro/sys/usr fields)
        │   ├── pipeline.py               # functional orchestrator + IterationMachine registry
        │   └── utils/                    # helper code that NEVER touches business logic
        │       ├── __init__.py
        │       ├── cli.py                # `python -m p.cli run examples/config.json`
        │       ├── display.py            # markdown & ANSI render helpers
        │       ├── token_count.py        # thin tiktoken wrapper
        │       ├── adapters/             # glue into Kedro, Kestra, LangSmith … optional
        │       │   ├── __init__.py
        │       │   └── kedro_kestra_adapter.py
        │       └── ui/                   # manual testing harnesses
        │           ├── __init__.py
        │           ├── gradio_harness.py
        │           └── streamlit_harness.py
        │
        │  # ── BB: Core-Generation stage group ───────────────────────────────────────
        ├── bb_core_generation/
        │   ├── __init__.py
        │   ├── #CLAUDE.md
        │   ├── ba_prompt_generator.py          # maps to “Prompt Generator” box
        │   ├── bb_test_case_generator.py       # “Test Case Generator”
        │   ├── bc_requirements_doc_generator.py# “Requirements Doc Generator”
        │   ├── bd_iteration_machine_1.py       # loops prompt-only; callable from pipeline
        │   └── synthetic_pipeline/             # fan-out sub-dir
        │       ├── __init__.py
        │       ├── #CLAUDE.md
        │       └── be_synthetic_test_generator.py
        │
        │  # ── CC: Testing & Evaluation ─────────────────────────────────────────────
        ├── cc_testing_evaluation/
        │   ├── __init__.py
        │   ├── #CLAUDE.md
        │   ├── ca_test_case_chunker.py         # “Test Case Chunker”
        │   ├── cb_test_executor.py             # “Test Executor”
        │   ├── cc_grader_generator.py          # “Grader Generator”
        │   └── cd_test_grader.py               # “Test Grader”
        │
        │  # ── DD: Refinement Pipeline ─────────────────────────────────────────────
        ├── dd_refinement/
        │   ├── __init__.py
        │   ├── #CLAUDE.md
        │   ├── da_feedback_summary.py          # “Feedback Summary”
        │   ├── db_prompt_refiner.py            # “Prompt Refiner”
        │   └── dc_iteration_machine_2.py       # loops on TC outputs vs expected
        │
        │  # ── EE: Optimisation Pipeline ───────────────────────────────────────────
        ├── ee_optimisation/
        │   ├── __init__.py
        │   ├── #CLAUDE.md
        │   ├── ea_token_optimizer.py           # “Token Optimizer”
        │   ├── eb_final_prompt_generation.py   # “Final Prompt Generation”
        │   └── ec_iteration_machine_3.py       # loops on graders for optimisation
        │
        │  # ── FF: Final Delivery ─────────────────────────────────────────────────
        ├── ff_final_delivery/
        │   ├── __init__.py
        │   ├── #CLAUDE.md
        │   └── fa_final_delivery.py            # bundles Final Prompt, TC doc, Req doc
        │
        │  # ── ZZ: Aux - tasks & agent log ─────────────────────────────────────────
        ├── tasks/
        │   ├── __init__.py
        │   ├── #CLAUDE.md
        │   └── ztasks.md                       # rolling agent/LLM to-do & done log
        │
        └── # end p/
```

### Commentary & rules

* **Letter prefixes** (`aa_`, `ba_`, `bb_`, …) keep every file/directory in pipeline order.

  Stage-internal files inherit the stage’s first letter (`ba_`, `bb_` inside `bb_core_generation`).

  You still have “alphabetical slack” (skipping letters) if new steps appear.
* **`#CLAUDE.md`**

  Each package (any dir with `__init__.py`) contains a short Markdown file:

  *Purpose, boundaries, extension checklist, and link back to the flow-chart section.*
* **Cross-cutting code renamed to `aa_core/`**

  Holds shared models, prompts, pipeline, and utils; lives first so imports never circular.
* **Iteration Machines**

  * `bd_iteration_machine_1.py` loops on the prompt only.
  * `dc_iteration_machine_2.py` fires after grades → feeds `db_prompt_refiner.py`.
  * `ec_iteration_machine_3.py` tweaks optimised prompt based on grader feedback.

    Each exposes a single `def iterate()` callable that the orchestrator can plug in.
* **Pydantic model placement**

  * Stage-specific schema stays right beside its logic (locality).
  * Any schema passed between **two** stages is re-exported from `aa_core/models.py` to prevent import cycles.
* **Adapters & UI**

  Kept under `aa_core/utils/` so core business logic never depends on optional stacks.
* **One `pyproject.toml`**

  * uv (the fast pip replacement) and pip both read it; no need for per-package tomls.
  * If you ever split into a mono-repo with independent wheels, *then* add more, otherwise keep it single.
* **Tests**

  `tests/unit/` one-to-one with stage files, `tests/e2e/` drives `aa_core.pipeline`.
* **No Poetry**

  You install with **`pip install -r requirements.txt`** *or* `uv pip install -r requirements.txt` (uv drop-in).

  Generate the lock via `uv pip compile pyproject.toml > requirements.lock`.
* **Notebooks**

  Live top-level, never in `src/`; first cell can `sys.path.append("src")`.
* **`.tmp/`**

  Root-level scratch zone; keep outside `src/` to avoid accidental imports.

This tree and commentary “lock in” the architecture while leaving every stage independent, strictly typed, and discoverable—exactly what the Duplo-block rules demand.
