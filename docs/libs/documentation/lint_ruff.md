# Ruff Linter Documentation

## Overview
Ruff is an extremely fast Python linter and code formatter, written in Rust. It's designed to be a drop-in replacement for tools like Flake8, isort, and Black.

## Configuration Location
All Ruff configuration is in `pyproject.toml` under `[tool.ruff]` sections.

## Our Configuration

### Basic Settings
```toml
[tool.ruff]
line-length = 150  # AF preference for longer lines
target-version = "py312"  # Should match requires-python
```

### Lint Configuration
```toml
[tool.ruff.lint]
select = ["ALL"]  # Enable all rules, then selectively ignore

ignore = [
    # Documentation
    "D",       # All docstring rules (we handle separately)
    
    # Formatting
    "COM812",  # Trailing comma (formatter handles)
    "ISC001",  # Implicit string concat
    "E501",    # Line too long (formatter handles)
    
    # Exceptions
    "TRY003",  # Long exception messages
    "EM101",   # Raw strings in exceptions
    "EM102",   # f-strings in exceptions
    "TRY300",  # try-except-pass allowed
    
    # TODOs
    "FIX002",  # TODO comments allowed
    "TD002",   # Missing author in TODO
    "TD003",   # Missing issue link in TODO
    
    # Boolean args
    "FBT001",  # Boolean positional args
    "FBT002",  # Boolean default values
    "FBT003",  # Boolean positional calls
    
    # Other
    "G004",    # Logging f-strings
    "ERA001",  # Commented-out code
    
    # Brittle Code Philosophy
    "S101",    # Assert statements (fail fast)
    "BLE001",  # Blind exceptions (surface errors)
    "TRY203",  # Re-raising exceptions
    "T201",    # Print statements
    "E402",    # Import order flexibility
]
```

### Complexity Limits
```toml
[tool.ruff.lint.pylint]
max-statements = 50      # Lines per function/method
max-branches = 12        # Decision points per function
max-args = 7            # Function arguments
max-locals = 15         # Local variables
max-public-methods = 20 # Public methods per class

[tool.ruff.lint.mccabe]
max-complexity = 10     # McCabe cyclomatic complexity
```

### Per-File Ignores
```toml
[tool.ruff.lint.per-file-ignores]
# Tests - relaxed rules
"**/tests/**/*test*.py" = ["F401", "ANN001", "ANN002", "ANN003", "ARG001", "ANN201", "PLR2004", "S101", "S106", "SLF001", "S105"]

# Examples - relaxed rules
"examples/**" = ["ANN001", "ANN002", "ANN003", "ARG001", "ANN201", "PLR2004", "S101", "S106", "SLF001", "S105", "T201"]

# Doc generation - special naming
"src/pfc/doc_gen/d*.py" = ["N999"]

# Notebooks - special rules
"*.ipynb" = ["E402", "F811"]

# Scripts - no __init__.py required
"scripts/*" = ["INP001"]

# Archives - ignore everything
".archive/*" = ["ALL"]
```

### Format Configuration
```toml
[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"
```

## Common Commands

```bash
# Check all files
ruff check .

# Check specific file
ruff check path/to/file.py

# Check with specific rules
ruff check --select C901,PLR0915

# Fix auto-fixable issues
ruff check --fix

# Format code
ruff format .

# Show rule explanation
ruff rule PLR0915
```

## Key Rule Categories

- **C90x**: McCabe complexity
- **PLR**: Pylint Refactor rules
- **S**: Security (Bandit)
- **T**: Type checking
- **ANN**: Annotations
- **D**: Documentation/Docstrings
- **E/W**: pycodestyle errors/warnings
- **F**: Pyflakes
- **N**: pep8-naming

## Integration

### VS Code
- Install "Ruff" extension by Charlie Marsh
- Auto-formats on save if configured

### Pre-commit
```yaml
- repo: https://github.com/astral-sh/ruff-pre-commit
  rev: v0.12.2
  hooks:
    - id: ruff
      args: [--fix]
    - id: ruff-format
```

### CI/CD
```bash
# In GitHub Actions
- name: Lint with ruff
  run: |
    pip install ruff
    ruff check .
    ruff format --check .
```

## Troubleshooting

### TOML Parse Errors
- Check bracket matching
- Ensure proper indentation
- Verify string quotes are closed
- Check for duplicate sections

### Rule Conflicts
- D203/D211: Choose one blank line style
- D212/D213: Choose one docstring style
- Use `ignore` to disable conflicting rules

### Performance
- Use `extend-exclude` to skip large directories
- Ruff is fast but skipping `.venv`, `node_modules` helps

## Philosophy Notes

Our configuration follows "purposeful brittleness":
- No hiding errors (no BLE001)
- Fail fast with asserts (allow S101)
- Surface all issues during development
- Explicit over implicit