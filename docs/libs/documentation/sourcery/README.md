# Sourcery Documentation

This directory contains comprehensive documentation for Sourcery, a code quality and refactoring tool for Python.

## Documentation Structure

### 📄 [Configuration Overview](./configuration-overview.md)
Complete guide to `.sourcery.yaml` configuration including:
- Configuration file structure and location
- All available configuration options
- Rule settings and management
- GitHub integration settings
- Code quality metrics
- Examples and best practices

### 📄 [Custom Rules Guide](./custom-rules-guide.md)
In-depth tutorial for writing custom Sourcery rules:
- Rule anatomy and required/optional fields
- Pattern syntax reference
- Step-by-step rule creation
- Advanced patterns and conditions
- Real-world examples
- Testing and debugging rules

### 📄 [Pattern Syntax Examples](./pattern-syntax-examples.md)
Extensive collection of pattern examples:
- Basic patterns and captures
- Multiple captures (* and +)
- Wildcards and missing code detection
- Control flow patterns
- Complex multi-line patterns
- Domain-specific examples (Django, testing, etc.)

### 📄 [Quick Reference](./quick-reference.md)
Concise cheat sheet for daily use:
- Configuration file template
- Pattern syntax quick reference
- Common rule patterns
- CLI commands
- Debugging tips

## Getting Started

1. **Install Sourcery**: Follow installation instructions from [sourcery.ai](https://sourcery.ai)

2. **Initialize Configuration**:
   ```bash
   sourcery init
   ```

3. **Create Custom Rules**: Start with the [Custom Rules Guide](./custom-rules-guide.md)

4. **Test Your Rules**:
   ```bash
   sourcery review --config .sourcery.yaml .
   ```

## Key Concepts

### Pattern Syntax
- `${var}` - Capture single expression
- `${var*}` - Capture zero or more expressions  
- `${var+}` - Capture one or more expressions
- `...` - Match any code without capturing
- `!!!` - Match missing/absent code

### Rule Structure
```yaml
rules:
  - id: unique-identifier
    pattern: code_to_match
    description: Brief explanation
    replacement: improved_code  # optional
    explanation: |
      Detailed explanation with **Markdown**
    tags: [category]
    tests:
      - match: should_trigger
      - no-match: should_not_trigger
```

### Configuration Hierarchy
1. Command line flags (highest priority)
2. Project config (`.sourcery.yaml`)
3. User config
4. Defaults (lowest priority)

## Common Use Cases

### Code Quality
- Enforce coding standards
- Detect anti-patterns
- Suggest best practices
- Automate refactoring

### Team Collaboration
- Share rules via version control
- Maintain consistent code style
- Document team conventions
- Automated code review

### Framework-Specific
- Django optimizations
- FastAPI patterns
- Pandas best practices
- Testing improvements

## Resources

- **Official Docs**: https://docs.sourcery.ai/
- **GitHub**: https://github.com/sourcery-ai/sourcery
- **Community Rules**: https://github.com/sourcery-ai/sourcery-rules
- **VS Code Extension**: [Marketplace](https://marketplace.visualstudio.com/items?itemName=sourcery.sourcery)

## Contributing

When adding new documentation:
1. Follow the existing structure
2. Include practical examples
3. Test all code snippets
4. Update this README

## Version

This documentation is current as of Sourcery version available in 2024. Check the official documentation for the latest updates.