# Sourcery Rules Configuration Guide

## Overview

This document provides a comprehensive guide to Sourcery's 160+ Python refactoring rules, organized by category and with recommendations for enabling/disabling based on common use cases.

## Rule Categories

### 1. Refactorings (Automatic)
These rules change code structure without changing behavior. Sourcery applies these automatically when enabled.

### 2. Suggestions (Manual)
These rules may change code behavior and require manual approval. They guide best practices but need careful review.

### 3. No-replacement Rules (Flagging Only)
These rules identify issues but don't provide automatic fixes, requiring manual intervention.

## Comprehensive Rules List

### Assignment & Variable Management

| Rule ID | Description | Category | Recommended | Reason |
|---------|-------------|----------|-------------|---------|
| `aug-assign` | Use augmented assignment (x += 1) | Refactoring | ✅ Enable | Cleaner, more Pythonic |
| `assign-if-exp` | Use conditional expressions (ternary) | Refactoring | ✅ Enable | More concise for simple conditions |
| `inline-variable` | Remove unnecessary intermediate variables | Refactoring | ✅ Enable | Reduces cognitive load |
| `inline-immediately-returned-variable` | Return expressions directly | Refactoring | ✅ Enable | Cleaner code |
| `default-mutable-arg` | Avoid mutable default arguments | Suggestion | ✅ Enable | Prevents common bugs |

### Control Flow & Conditionals

| Rule ID | Description | Category | Recommended | Reason |
|---------|-------------|----------|-------------|---------|
| `remove-redundant-if` | Remove unnecessary if statements | Refactoring | ✅ Enable | Simplifies logic |
| `merge-duplicate-blocks` | Combine identical if/elif blocks | Refactoring | ✅ Enable | DRY principle |
| `merge-comparisons` | Combine multiple comparisons | Refactoring | ✅ Enable | More readable |
| `de-morgan` | Apply De Morgan's laws | Refactoring | ⚠️ Context | Can reduce readability |
| `simplify-boolean-comparison` | Remove redundant boolean comparisons | Refactoring | ✅ Enable | Cleaner code |
| `boolean-if-exp-identity` | Simplify boolean if expressions | Refactoring | ✅ Enable | Removes redundancy |
| `hoist-statement-from-if` | Move duplicated statements out of conditionals | Refactoring | ✅ Enable | DRY principle |

### Collections & Comprehensions

| Rule ID | Description | Category | Recommended | Reason |
|---------|-------------|----------|-------------|---------|
| `list-comprehension` | Use list comprehensions | Refactoring | ✅ Enable | More Pythonic |
| `dict-comprehension` | Use dictionary comprehensions | Refactoring | ✅ Enable | More Pythonic |
| `set-comprehension` | Use set comprehensions | Refactoring | ✅ Enable | More Pythonic |
| `collection-builtin-to-comprehension` | Convert list()/dict()/set() to comprehensions | Refactoring | ✅ Enable | More efficient |
| `collection-into-set` | Use set for membership testing | Refactoring | ✅ Enable | O(1) lookups |
| `comprehension-to-generator` | Replace unneeded comprehension with generator | Refactoring | ✅ Enable | Memory efficient |
| `convert-any-to-in` | Convert any() to 'in' operator | Refactoring | ✅ Enable | Simpler syntax |
| `convert-to-enumerate` | Use enumerate() instead of manual counter | Refactoring | ✅ Enable | Pythonic iteration |

### String Operations

| Rule ID | Description | Category | Recommended | Reason |
|---------|-------------|----------|-------------|---------|
| `use-fstring` | Use f-strings for formatting | Refactoring | ✅ Enable | Modern Python (3.6+) |
| `use-join` | Use join() instead of concatenation | Refactoring | ✅ Enable | More efficient |
| `remove-str-from-print` | Remove unnecessary str() in print | Refactoring | ✅ Enable | Cleaner code |
| `simplify-str-format` | Simplify string formatting | Refactoring | ✅ Enable | More readable |

### Function & Method Improvements

| Rule ID | Description | Category | Recommended | Reason |
|---------|-------------|----------|-------------|---------|
| `default-get` | Use dict.get() with default | Refactoring | ✅ Enable | Cleaner than if-else |
| `use-contextlib-suppress` | Replace try-except-pass with suppress | Refactoring | ⚠️ Context | Depends on clarity needs |
| `use-named-expression` | Use walrus operator (Python 3.8+) | Refactoring | ⚠️ Context | Can reduce readability |
| `extract-method` | Extract repeated code into functions | Suggestion | ✅ Enable | DRY principle |
| `remove-pass` | Remove unnecessary pass statements | Refactoring | ✅ Enable | Cleaner code |

### Performance Optimizations

| Rule ID | Description | Category | Recommended | Reason |
|---------|-------------|----------|-------------|---------|
| `use-min-max` | Use min()/max() built-ins | Refactoring | ✅ Enable | More efficient |
| `use-sum` | Use sum() for adding lists | Refactoring | ✅ Enable | More efficient |
| `simplify-negative-index` | Use negative indexing | Refactoring | ✅ Enable | More Pythonic |
| `bin-op-identity` | Simplify binary operations (x | x → x) | Refactoring | ✅ Enable | Removes redundancy |

### Code Quality & Best Practices

| Rule ID | Description | Category | Recommended | Reason |
|---------|-------------|----------|-------------|---------|
| `avoid-builtin-shadow` | Don't shadow built-in names | No-replacement | ✅ Enable | Prevents confusion |
| `require-parameter-annotation` | Require type hints for parameters | Suggestion | ⚠️ Context | Team preference |
| `require-return-annotation` | Require return type hints | Suggestion | ⚠️ Context | Team preference |
| `no-unnecessary-cast` | Remove redundant type conversions | Refactoring | ✅ Enable | Cleaner code |
| `path-read` | Use pathlib for file operations | Refactoring | ✅ Enable | Modern Python |

### Loop & Iteration Patterns

| Rule ID | Description | Category | Recommended | Reason |
|---------|-------------|----------|-------------|---------|
| `for-index-underscore` | Use _ for unused loop variables | Refactoring | ✅ Enable | Convention |
| `while-to-for` | Convert while loops to for loops | Refactoring | ⚠️ Context | Depends on use case |
| `use-any` | Use any() for existence checks | Refactoring | ✅ Enable | More Pythonic |
| `use-all` | Use all() for universal checks | Refactoring | ✅ Enable | More Pythonic |

## Recommended Configuration

### For General Python Projects

```yaml
# .sourcery.yaml
rule_settings:
  enable:
    - default  # All default refactorings
  disable:
    - use-named-expression  # Walrus operator can reduce readability
    - de-morgan  # Can make complex conditions harder to understand
    - require-parameter-annotation  # Unless using strict typing
    - require-return-annotation  # Unless using strict typing
  rule_types:
    - refactoring
    - suggestion
    - comment
```

### For Strict Type-Checked Projects

```yaml
# .sourcery.yaml
rule_settings:
  enable:
    - default
    - google-python-style-guide
    - require-parameter-annotation
    - require-return-annotation
  disable:
    - use-named-expression  # Team preference
  rule_types:
    - refactoring
    - suggestion
    - comment
```

### For Legacy Codebases

```yaml
# .sourcery.yaml
rule_settings:
  enable:
    - default
  disable:
    - use-fstring  # If supporting Python < 3.6
    - use-named-expression  # If supporting Python < 3.8
    - path-read  # If not ready for pathlib migration
  rule_types:
    - refactoring
    - suggestion
```

### For Performance-Critical Code

```yaml
# .sourcery.yaml
rule_settings:
  enable:
    - default
    - collection-into-set  # O(1) lookups
    - comprehension-to-generator  # Memory efficiency
    - use-min-max  # Built-in optimizations
    - use-sum  # Built-in optimizations
  disable:
    - extract-method  # May add function call overhead
  rule_types:
    - refactoring
    - suggestion
```

## Rule Packages

### Google Python Style Guide

Enable with: `--enable google-python-style-guide`

Includes additional rules for:
- Naming conventions
- Import ordering
- Documentation requirements
- Line length limits
- Specific Python idioms

## Inline Rule Control

### Skip All Rules for a Function

```python
def complex_legacy_function():  # sourcery skip
    # This function won't be analyzed
    pass
```

### Skip Specific Rules

```python
def special_function():  # sourcery skip: use-fstring, aug-assign
    # Only specified rules are skipped
    old_style = "Hello " + name
    count = count + 1
```

### Skip Rules for a File

```python
# sourcery skip: use-fstring
# At the top of the file - affects entire file
```

## Best Practices for Rule Configuration

1. **Start Conservative**: Enable default rules first, add more gradually
2. **Team Agreement**: Discuss controversial rules (walrus operator, De Morgan's)
3. **Project Consistency**: Align with existing code style
4. **Python Version**: Disable rules for unsupported Python features
5. **Review Suggestions**: Always review suggestions before applying
6. **Document Decisions**: Comment why specific rules are disabled

## Integration with CI/CD

### Pre-commit Configuration

```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/sourcery-ai/sourcery
    rev: v1.0.0
    hooks:
      - id: sourcery
        args: 
          - --config=.sourcery.yaml
          - --diff=origin/main  # Only check changed code
```

### GitHub Actions Configuration

```yaml
# .github/workflows/sourcery.yml
name: Sourcery
on: [pull_request]

jobs:
  sourcery:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run Sourcery
        uses: sourcery-ai/sourcery-action@v1
        with:
          token: ${{ secrets.SOURCERY_TOKEN }}
```

## Troubleshooting Common Issues

### Rule Not Applying

1. Check if rule is enabled in configuration
2. Verify Python version compatibility
3. Check for inline skip comments
4. Ensure file is included (not in exclude patterns)

### Too Many Suggestions

1. Use `--diff` to only check new code
2. Disable aggressive rules temporarily
3. Focus on one category at a time
4. Use incremental adoption approach

### Performance Issues

1. Exclude large generated files
2. Use file patterns to limit scope
3. Run on changed files only in CI
4. Consider parallel processing options

## Additional Resources

- [Official Documentation](https://docs.sourcery.ai)
- [Rule Examples](https://github.com/sourcery-ai/sourcery/wiki/Current-Refactorings)
- [Custom Rules Guide](https://docs.sourcery.ai/Tutorials/Custom-Rules/)
- [VS Code Extension](https://marketplace.visualstudio.com/items?itemName=sourcery.sourcery)