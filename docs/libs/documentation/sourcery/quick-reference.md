# Sourcery Quick Reference

A concise reference for Sourcery configuration and custom rules.

## Configuration File Template

```yaml
# .sourcery.yaml

# Ignore files/directories
ignore:
  - tests/
  - .venv/
  - "*.test.py"

# Rule settings
rule_settings:
  enable: [default]
  disable: [rule-id-to-disable]
  rule_types: [refactoring, suggestion, comment]
  python_version: '3.9'

# Custom rules
rules:
  - id: rule-id
    pattern: code_to_match
    description: What this rule does
    replacement: improved_code  # optional

# Metrics
metrics:
  quality_threshold: 25.0

# GitHub settings  
github:
  request_review: author
  sourcery_branch: sourcery/{base_branch}
```

## Pattern Syntax Cheat Sheet

| Syntax | Description | Example |
|--------|-------------|---------|
| `${var}` | Capture single expression | `print(${msg})` |
| `${var*}` | Capture zero or more | `func(${args*})` |
| `${var+}` | Capture one or more | `[${items+}]` |
| `...` | Match any code | `def f(...): ...` |
| `!!!` | Match missing code | `"""!!!"""` |

## Common Rule Patterns

### Type Checking
```yaml
- id: use-isinstance
  pattern: type(${obj}) == ${type}
  replacement: isinstance(${obj}, ${type})
```

### List Operations
```yaml
- id: use-list-comprehension
  pattern: |
    ${result} = []
    for ${x} in ${items}:
        ${result}.append(${expr})
  replacement: ${result} = [${expr} for ${x} in ${items}]
```

### String Formatting
```yaml
- id: use-f-string
  pattern: "${str}".format(${args*})
  replacement: f"${str}"
```

### File Operations
```yaml
- id: remove-redundant-open-mode
  pattern: open(${file}, "r")
  replacement: open(${file})
```

## Testing Rules

```yaml
rules:
  - id: example-rule
    pattern: old_pattern
    replacement: new_pattern
    tests:
      - match: old_pattern  # Should trigger
      - no-match: new_pattern  # Should not trigger
```

## CLI Commands

```bash
# Initialize config
sourcery init

# Review code
sourcery review .

# Review with specific config
sourcery review --config .sourcery.yaml .

# See diff of changes
sourcery review --diff file.py

# Fix code automatically
sourcery review --fix file.py
```

## Rule Tags

Common tags for organizing rules:
- `performance`
- `readability`
- `security`
- `best-practice`
- `refactoring`
- Framework-specific: `django`, `flask`, `fastapi`
- Library-specific: `pandas`, `numpy`, `requests`

## Path Configuration

```yaml
rules:
  - id: rule-id
    pattern: pattern
    paths:
      include:
        - "src/"
        - "*.py"
      exclude:
        - "tests/"
        - "*_generated.py"
```

## Conditions

```yaml
rules:
  - id: conditional-rule
    pattern: ${func}(${arg})
    condition: ${func}.name == "print"
    replacement: logger.info(${arg})
```

## Multi-line Patterns

```yaml
pattern: |
  if ${condition}:
      ${true_body+}
  else:
      ${false_body+}
```

## Debug Tips

1. **Rule not matching?**
   - Simplify pattern
   - Check captures are consistent
   - Verify syntax

2. **Too many matches?**
   - Add conditions
   - Use more specific patterns
   - Exclude paths

3. **Test your rules:**
   ```bash
   sourcery review --verbose --config .sourcery.yaml test_file.py
   ```

## Example: Complete Rule

```yaml
rules:
  - id: no-mutable-defaults
    pattern: |
      def ${func}(${params1*}, ${param}=${default}, ${params2*}):
          ${body+}
    condition: ${default}.type in ["list", "dict", "set"]
    description: Don't use mutable default arguments
    explanation: |
      Mutable defaults are shared between function calls.
      Use None and create inside function instead.
    replacement: |
      def ${func}(${params1*}, ${param}=None, ${params2*}):
          if ${param} is None:
              ${param} = ${default}
          ${body+}
    tags: [bug-risk, best-practice]
    tests:
      - match: "def f(x=[]):"
      - no-match: "def f(x=None):"
```

## Configuration Hierarchy

1. Command line flags (highest priority)
2. Project config (`.sourcery.yaml`)
3. User config
4. Defaults (lowest priority)

## Useful Resources

- Docs: https://docs.sourcery.ai/
- Pattern Syntax: https://docs.sourcery.ai/coding-assistant/reference/pattern-syntax/
- Rule Examples: https://github.com/sourcery-ai/sourcery-rules