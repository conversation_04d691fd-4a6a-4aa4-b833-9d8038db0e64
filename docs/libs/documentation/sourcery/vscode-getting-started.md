# Getting Started with Sourcery for VS Code

## Installing Sourcery

There are two ways to install the Sourcery extension:

1. **Direct Installation**: Open the [Sourcery extension in VS Code](https://marketplace.visualstudio.com/items?itemName=sourcery.sourcery) and click install

2. **Via VS Code Extensions**:
   - Go to VS Code _Preferences_
   - Select _Extensions_
   - Search for "Sourcery"
   - Click _Install_

## Logging In

To log in to Sourcery:

1. Open the command palette (`Ctrl+Shift+P` on Windows/Linux or `Cmd+Shift+P` on macOS)
2. Run the command: `Sourcery: Login`
3. Copy your token from the [Sourcery dashboard](https://app.sourcery.ai/dashboard)
4. Paste the token in the VS Code Sourcery settings section

**Note**: If you don't have an account yet, you can sign up at the [Sourcery dashboard](https://app.sourcery.ai/dashboard).

## VS Code Specific Features

### Suggest AI Fixes

Sourcery can automatically suggest fixes for problems in your code:

- **Default Behavior**: Sourcery suggests fixes for problems shown in the Problems pane
- **How to Use**:
  1. Activate the code actions menu on a problem
  2. Choose the 'Sourcery - Fix in Chat' option
  3. This opens the Sourcery AI assistant to explain and fix the problem
- **Configuration**: You can toggle this feature on/off in VS Code settings via the `suggestFixes` setting

### Suggest Improvements to Complex Functions

Sourcery helps improve complex functions:

- **Code Metrics Hover**: When you hover over complex functions, Sourcery displays code metrics
- **Improvement Suggestions**: Click on the suggestions to have Sourcery propose implementations in the chat interface
- This feature helps maintain clean, readable code by identifying and fixing complexity issues

## Next Steps

After installation and login, you're ready to start using Sourcery to:
- Get AI-powered code suggestions
- Fix code problems automatically
- Improve complex functions
- Write better, cleaner code with less effort

For more detailed information and advanced features, visit the [Sourcery documentation](https://docs.sourcery.ai/).