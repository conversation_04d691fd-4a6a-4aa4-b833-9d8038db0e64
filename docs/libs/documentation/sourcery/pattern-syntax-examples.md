# Sourcery Pattern Syntax Examples

A comprehensive collection of pattern examples for writing Sourcery custom rules.

## Basic Patterns

### Literal Matching

```yaml
# Exact string match
pattern: print("debug")
# Matches: print("debug")

# Variable assignment
pattern: DEBUG = True
# Matches: DEBUG = True

# Function call
pattern: exit(0)
# Matches: exit(0)
```

### Simple Captures

```yaml
# Capture function argument
pattern: print(${message})
# Matches: print("hello"), print(x), print(data[0])
# Captures: message = "hello", x, data[0]

# Capture variable name
pattern: ${var} = None
# Matches: x = None, user_data = None
# Captures: var = x, user_data

# Capture both sides of assignment
pattern: ${var} = ${value}
# Matches: x = 5, name = "John"
# Captures: var = x, value = 5
```

## Multiple Captures

### Zero or More (*) 

```yaml
# Function with any number of arguments
pattern: print(${args*})
# Matches: print(), print(x), print(x, y, z)
# Captures: args = [], [x], [x, y, z]

# Class with optional base classes
pattern: class ${name}(${bases*}):
# Matches: class Dog:, class Dog(Animal):, class Dog(Animal, Pet):
# Captures: bases = [], [Animal], [Animal, Pet]

# List literal
pattern: [${items*}]
# Matches: [], [1], [1, 2, 3]
# Captures: items = [], [1], [1, 2, 3]
```

### One or More (+)

```yaml
# Function with required arguments
pattern: max(${values+})
# Matches: max(a), max(a, b, c)
# Does NOT match: max()

# Non-empty list
pattern: [${items+}]  
# Matches: [1], [1, 2, 3]
# Does NOT match: []

# Import statement
pattern: from ${module} import ${names+}
# Matches: from os import path, from django import forms, models
```

## Wildcards (...)

```yaml
# Any function definition
pattern: def ${name}(...): ...
# Matches: def foo():, def bar(x, y=1):, def baz(*args, **kwargs):

# Any class definition
pattern: class ${name}(...): ...
# Matches: class Dog:, class Cat(Animal):, class Bird(Animal, Flying):

# Any if statement
pattern: if ${condition}: ...
# Matches: if x > 0:, if user.is_authenticated():

# Try-except with any exception handling
pattern: |
  try:
      ...
  except ${exception}:
      ...
# Matches any try-except block
```

## Missing Code (!!!)

```yaml
# Function without return type
pattern: def ${name}(${params*}) -> !!!:
# Matches: def add(a, b):
# Does NOT match: def add(a, b) -> int:

# Class without docstring
pattern: |
  class ${name}(${bases*}):
      """!!!"""
# Matches classes without docstrings

# Function without docstring
pattern: |
  def ${name}(${params*}):
      """!!!"""
      ${body+}
# Matches functions without docstrings
```

## Method and Attribute Patterns

```yaml
# Method call
pattern: ${obj}.${method}()
# Matches: user.save(), data.clear()

# Method call with arguments
pattern: ${obj}.${method}(${args*})
# Matches: list.append(1), string.replace("a", "b")

# Attribute access
pattern: ${obj}.${attr}
# Matches: user.name, request.method

# Chained calls
pattern: ${obj}.${method1}().${method2}()
# Matches: query.filter().first(), string.strip().lower()

# Nested attribute access
pattern: ${obj}.${attr1}.${attr2}
# Matches: request.user.email, self.data.value
```

## Control Flow Patterns

### If Statements

```yaml
# Simple if
pattern: |
  if ${condition}:
      ${body+}

# If-else
pattern: |
  if ${condition}:
      ${true_body+}
  else:
      ${false_body+}

# If-elif-else
pattern: |
  if ${cond1}:
      ${body1+}
  elif ${cond2}:
      ${body2+}
  else:
      ${body3+}

# Nested condition
pattern: if ${var} is None:
# Matches: if x is None:, if user is None:
```

### Loops

```yaml
# Simple for loop
pattern: |
  for ${item} in ${iterable}:
      ${body+}

# For loop with list append
pattern: |
  for ${item} in ${iterable}:
      ${list}.append(${expr})

# While loop
pattern: |
  while ${condition}:
      ${body+}

# List comprehension target
pattern: [${expr} for ${item} in ${iterable}]
```

### Exception Handling

```yaml
# Basic try-except
pattern: |
  try:
      ${try_body+}
  except ${exception}:
      ${except_body+}

# Try-except with as
pattern: |
  try:
      ${try_body+}
  except ${exception} as ${var}:
      ${except_body+}

# Bare except
pattern: |
  try:
      ${body+}
  except:
      pass

# Multiple except blocks
pattern: |
  try:
      ${try_body+}
  except ${exc1}:
      ${body1+}
  except ${exc2}:
      ${body2+}
```

## Function and Class Patterns

### Function Definitions

```yaml
# Simple function
pattern: |
  def ${name}(${params*}):
      ${body+}

# Function with return
pattern: |
  def ${name}(${params*}):
      return ${value}

# Function with decorators (lenient matching)
pattern: |
  def ${name}(${params*}):
      ${body+}
# Also matches: @decorator def name():

# Async function
pattern: |
  async def ${name}(${params*}):
      ${body+}
```

### Class Definitions

```yaml
# Simple class
pattern: |
  class ${name}:
      ${body+}

# Class with inheritance
pattern: |
  class ${name}(${base}):
      ${body+}

# Class with __init__
pattern: |
  class ${name}(${bases*}):
      def __init__(self, ${params*}):
          ${init_body+}

# Dataclass pattern
pattern: |
  @dataclass
  class ${name}:
      ${fields+}
```

## Import Patterns

```yaml
# Simple import
pattern: import ${module}
# Matches: import os, import json

# From import
pattern: from ${module} import ${name}
# Matches: from os import path

# Multiple imports
pattern: from ${module} import ${names+}
# Matches: from django import forms, models

# Wildcard import
pattern: from ${module} import *

# Aliased import
pattern: import ${module} as ${alias}
# Matches: import numpy as np

# Relative import
pattern: from .${module} import ${name}
```

## String Patterns

```yaml
# F-string
pattern: f"${content}"
# Matches: f"Hello {name}"

# Format string
pattern: "${string}".format(${args*})
# Matches: "Hello {}".format(name)

# Percent formatting
pattern: "${string}" % ${args}
# Matches: "Hello %s" % name

# String concatenation
pattern: ${str1} + ${str2}
# When str1 and str2 are strings
```

## Comparison Patterns

```yaml
# Type checking
pattern: type(${obj}) == ${type}
# Matches: type(x) == list

# None checking
pattern: ${var} == None
# Matches: x == None

# Boolean comparison
pattern: ${var} == True
# Matches: flag == True

# Length checking
pattern: len(${obj}) == 0
# Matches: len(list) == 0

# In operator
pattern: ${item} in ${container}
# Matches: x in list, "key" in dict
```

## Complex Multi-line Patterns

### Consecutive Statements

```yaml
# Sequential appends
pattern: |
  ${list}.append(${item1})
  ${list}.append(${item2})

# Variable initialization pattern
pattern: |
  ${var} = []
  for ${item} in ${iterable}:
      ${var}.append(${expr})

# Open-close pattern
pattern: |
  ${file} = open(${path})
  ${body+}
  ${file}.close()
```

### Nested Structures

```yaml
# Nested if in loop
pattern: |
  for ${item} in ${iterable}:
      if ${condition}:
          ${body+}

# Function with nested function
pattern: |
  def ${outer}(${params1*}):
      def ${inner}(${params2*}):
          ${inner_body+}
      ${outer_body+}
```

## Advanced Patterns

### Using Conditions

```yaml
# Match only list/dict/set types
pattern: ${var} = ${value}
condition: ${value}.type in ["list", "dict", "set"]

# Match specific function names
pattern: ${func}(${args*})
condition: ${func}.name in ["print", "pprint"]

# Match string literals only
pattern: ${func}(${arg})
condition: ${arg}.type == "string"
```

### Optional Elements

```yaml
# Optional else clause
pattern: |
  if ${condition}:
      ${if_body+}
  ${else_clause?}

# Optional parameters
pattern: def ${name}(${required}, ${optional?}):
```

## Real-World Pattern Examples

### Django Patterns

```yaml
# get() with DoesNotExist
pattern: |
  try:
      ${var} = ${model}.objects.get(${query})
  except ${model}.DoesNotExist:
      ${handle}

# QuerySet iteration
pattern: |
  for ${item} in ${model}.objects.all():
      ${body+}

# Form validation
pattern: |
  if ${form}.is_valid():
      ${form}.save()
```

### Data Science Patterns

```yaml
# Pandas iterrows
pattern: |
  for ${idx}, ${row} in ${df}.iterrows():
      ${body+}

# NumPy array creation
pattern: np.array([${items+}])

# Matplotlib plotting
pattern: |
  plt.${plot_func}(${args*})
  plt.show()
```

### Testing Patterns

```yaml
# assertEqual usage
pattern: self.assertEqual(${actual}, ${expected})

# pytest assertion
pattern: assert ${actual} == ${expected}

# Mock usage
pattern: |
  ${mock} = Mock()
  ${mock}.${method}.return_value = ${value}
```

## Pattern Testing Examples

```yaml
rules:
  - id: example-rule
    pattern: type(${x}) == ${type}
    replacement: isinstance(${x}, ${type})
    tests:
      # Basic matches
      - match: type(x) == list
      - match: type(user) == User
      - match: type(data[0]) == dict
      
      # Should not match
      - no-match: isinstance(x, list)
      - no-match: type(x) != list
      - no-match: type(x)
      
      # Edge cases  
      - match: type(func()) == str
      - match: type(a.b.c) == MyClass
```

## Tips for Writing Patterns

1. **Start simple**: Test with basic patterns before adding complexity
2. **Use captures wisely**: Only capture what you need for the replacement
3. **Test edge cases**: Include whitespace variations, nested code
4. **Consider lenient matching**: Remember decorators, type hints are ignored
5. **Use conditions**: Restrict matches to avoid false positives
6. **Multi-line indentation**: Ensure proper Python indentation in patterns

## Common Pitfalls

1. **Forgetting + vs ***: Use + when at least one element is required
2. **Over-specific patterns**: Start general, add specificity as needed  
3. **Whitespace issues**: Python indentation must be correct
4. **Capture reuse**: Ensure captures used in replacement exist in pattern
5. **Missing quotes**: String patterns need proper quoting