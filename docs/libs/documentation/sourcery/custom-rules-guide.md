# Sourcery Custom Rules Guide

A comprehensive guide to writing custom rules for Sourcery code analysis and refactoring.

## Table of Contents

1. [Introduction](#introduction)
2. [Rule Anatomy](#rule-anatomy)
3. [Pattern Syntax Reference](#pattern-syntax-reference)
4. [Writing Your First Rule](#writing-your-first-rule)
5. [Advanced Patterns](#advanced-patterns)
6. [Testing Rules](#testing-rules)
7. [Real-World Examples](#real-world-examples)
8. [Best Practices](#best-practices)

## Introduction

Sourcery custom rules allow you to:
- Enforce project-specific coding standards
- Automate repetitive code improvements
- Share best practices across your team
- Create domain-specific refactorings

Rules are defined in your `.sourcery.yaml` configuration file and can either suggest improvements or automatically fix code.

## Rule Anatomy

### Required Fields

Every rule must have these three fields:

```yaml
rules:
  - id: unique-rule-identifier
    pattern: code_to_match
    description: Brief explanation of the rule
```

### Optional Fields

Additional fields for more sophisticated rules:

```yaml
rules:
  - id: complete-rule-example
    pattern: code_pattern_to_match
    description: One-line description
    replacement: improved_code_pattern
    explanation: |
      Detailed explanation supporting the rule.
      Can use **Markdown** formatting.
    condition: boolean_condition
    tags:
      - performance
      - best-practice
    paths:
      include:
        - "src/"
      exclude:
        - "tests/"
    tests:
      - match: code_that_should_match
      - no-match: code_that_should_not_match
```

### Field Descriptions

- **id**: Unique identifier (alphanumeric with hyphens/underscores)
- **pattern**: Code pattern using Sourcery's pattern syntax
- **description**: Brief explanation shown in IDE
- **replacement**: Code to replace matches (optional - omit for detection-only rules)
- **explanation**: Detailed rationale with Markdown support
- **condition**: Boolean expression to restrict matches
- **tags**: Categories for organizing rules
- **paths**: Include/exclude specific paths
- **tests**: Validate rule behavior

## Pattern Syntax Reference

### Basic Patterns

1. **Literal Matching**
   ```yaml
   pattern: print("debug")
   # Matches: print("debug")
   ```

2. **Single Expression Capture**
   ```yaml
   pattern: len(${var})
   # Matches: len(x), len(my_list), len(data[0])
   # Captures the argument as ${var}
   ```

3. **Multiple Expression Capture**
   ```yaml
   pattern: print(${args*})
   # Matches: print(), print(x), print(x, y, z)
   # ${args*} captures zero or more arguments
   
   pattern: max(${values+})
   # Matches: max(a, b), max(1, 2, 3)
   # ${values+} requires at least one argument
   ```

4. **Wildcard Matching**
   ```yaml
   pattern: def ${name}(...): ...
   # Matches any function definition
   # ... matches any parameters/body without capturing
   ```

5. **Missing Code Detection**
   ```yaml
   pattern: |
     class ${name}:
         """!!!"""
   # Matches classes without docstrings
   # !!! matches when expected code is absent
   ```

### Lenient Matching

Sourcery ignores certain elements unless explicitly specified:

```yaml
# This pattern:
pattern: def add(a, b): return a + b

# Also matches:
# def add(a: int, b: int) -> int: return a + b
# @decorator
# def add(a, b): return a + b
# async def add(a, b): return a + b
```

### Method Calls and Attributes

```yaml
# Match any method call on an object
pattern: ${obj}.${method}(${args*})

# Match specific attribute access
pattern: ${obj}.length

# Match chained calls
pattern: ${obj}.${method1}().${method2}()
```

### Control Flow Patterns

```yaml
# If statements
pattern: |
  if ${condition}:
      ${body+}
  else:
      return None

# For loops
pattern: |
  for ${item} in ${iterable}:
      ${list}.append(${item})

# Try-except blocks
pattern: |
  try:
      ${body+}
  except:
      pass
```

## Writing Your First Rule

Let's create a rule step by step:

### Step 1: Identify the Pattern

Suppose we want to replace `type(x) == list` with `isinstance(x, list)`:

```python
# Bad
if type(user_data) == list:
    process_list(user_data)

# Good  
if isinstance(user_data, list):
    process_list(user_data)
```

### Step 2: Create the Pattern

```yaml
rules:
  - id: use-isinstance-for-type-check
    pattern: type(${obj}) == ${type}
    replacement: isinstance(${obj}, ${type})
    description: Use isinstance() for type checking
```

### Step 3: Add Explanation

```yaml
rules:
  - id: use-isinstance-for-type-check
    pattern: type(${obj}) == ${type}
    replacement: isinstance(${obj}, ${type})
    description: Use isinstance() for type checking
    explanation: |
      `isinstance()` is the Pythonic way to check types because:
      - It supports inheritance (subclasses)
      - It's more readable
      - It handles multiple types: isinstance(x, (int, float))
```

### Step 4: Add Tests

```yaml
rules:
  - id: use-isinstance-for-type-check
    pattern: type(${obj}) == ${type}
    replacement: isinstance(${obj}, ${type})
    description: Use isinstance() for type checking
    explanation: |
      `isinstance()` is the Pythonic way to check types
    tests:
      - match: type(data) == list
      - match: type(x) == MyClass
      - no-match: isinstance(x, list)
      - no-match: type(x)  # No comparison
```

## Advanced Patterns

### Using Conditions

Conditions allow you to restrict when a pattern matches:

```yaml
rules:
  - id: simplify-len-check
    pattern: len(${list}) > 0
    replacement: ${list}
    condition: ${list}.type == "list"
    description: Simplify length check for lists
```

### Complex Multi-line Patterns

```yaml
rules:
  - id: combine-append-statements
    pattern: |
      ${list}.append(${item1})
      ${list}.append(${item2})
    replacement: ${list}.extend([${item1}, ${item2}])
    description: Combine consecutive append calls
```

### Class and Function Patterns

```yaml
rules:
  - id: add-init-docstring
    pattern: |
      class ${name}(${bases*}):
          def __init__(self, ${params*}):
              """!!!"""
              ${body+}
    replacement: |
      class ${name}(${bases*}):
          def __init__(self, ${params*}):
              """Initialize ${name}."""
              ${body+}
    description: Add docstring to __init__ methods
```

### Import Patterns

```yaml
rules:
  - id: use-specific-imports
    pattern: from ${module} import *
    description: Avoid wildcard imports
    explanation: |
      Wildcard imports can:
      - Pollute namespace
      - Make code harder to understand
      - Cause naming conflicts
```

## Testing Rules

### Test Structure

Tests validate that your rules work correctly:

```yaml
tests:
  - match: |
      # Code that SHOULD trigger the rule
      if type(x) == list:
          process(x)
  
  - no-match: |
      # Code that should NOT trigger the rule
      if isinstance(x, list):
          process(x)
```

### Multi-line Test Cases

```yaml
tests:
  - match: |
      def calculate(data):
          result = []
          for item in data:
              result.append(item * 2)
          return result
```

### Edge Cases to Test

Always test:
1. Basic case
2. Edge cases
3. Similar but different patterns
4. Code that should not match

## Real-World Examples

### Example 1: Logging Best Practices

```yaml
rules:
  - id: use-lazy-logging
    pattern: ${logger}.${method}(f"${message}")
    replacement: ${logger}.${method}("${message}")
    condition: ${method} in ["debug", "info", "warning", "error", "critical"]
    description: Use lazy string formatting in logging
    explanation: |
      F-strings are evaluated immediately, even if the log level
      would filter out the message. Use lazy formatting instead:
      logger.info("User %s logged in", username)
    tests:
      - match: logger.info(f"User {user} logged in")
      - no-match: logger.info("User %s logged in", user)
```

### Example 2: Django Optimization

```yaml
rules:
  - id: use-get-object-or-404
    pattern: |
      try:
          ${var} = ${model}.objects.get(${query})
      except ${model}.DoesNotExist:
          raise Http404
    replacement: |
      ${var} = get_object_or_404(${model}, ${query})
    description: Use get_object_or_404 shortcut
    paths:
      include:
        - "views.py"
        - "*/views/*.py"
```

### Example 3: Pandas Best Practices

```yaml
rules:
  - id: avoid-pandas-iterrows
    pattern: |
      for ${index}, ${row} in ${df}.iterrows():
          ${body+}
    description: Avoid iterrows() - use vectorized operations
    explanation: |
      iterrows() is slow. Consider:
      - Vectorized operations: df['col'] = df['col'] * 2
      - apply(): df.apply(func, axis=1)
      - itertuples(): for row in df.itertuples()
```

### Example 4: Security Rules

```yaml
rules:
  - id: no-hardcoded-passwords
    pattern: ${var} = "${value}"
    condition: |
      ${var}.name.lower() in ["password", "secret", "api_key", "token"] and
      len("${value}") > 0
    description: Don't hardcode sensitive values
    explanation: |
      Use environment variables or configuration files:
      ```python
      import os
      password = os.environ.get('DB_PASSWORD')
      ```
```

### Example 5: Performance Optimization

```yaml
rules:
  - id: use-list-comprehension
    pattern: |
      ${result} = []
      for ${item} in ${iterable}:
          ${result}.append(${expr})
    replacement: |
      ${result} = [${expr} for ${item} in ${iterable}]
    description: Use list comprehension for simple loops
    condition: |
      # Only suggest if loop body is simple
      ${expr}.is_simple_expression()
```

## Best Practices

### 1. Start Simple

Begin with straightforward patterns and add complexity gradually:

```yaml
# Start with this
pattern: type(${x}) == list

# Before attempting this
pattern: |
  if type(${x}) == list and len(${x}) > 0:
      for ${item} in ${x}:
          ${body+}
```

### 2. Write Comprehensive Tests

```yaml
tests:
  # Test basic case
  - match: print("Hello")
  
  # Test with different quotes
  - match: print('Hello')
  
  # Test with variables
  - match: print(message)
  
  # Test what shouldn't match
  - no-match: logger.info("Hello")
```

### 3. Use Clear Descriptions

```yaml
# Bad
description: Fix code

# Good
description: Use isinstance() instead of type() comparison

# Better (includes the pattern)
description: Replace type(obj) == Type with isinstance(obj, Type)
```

### 4. Provide Helpful Explanations

```yaml
explanation: |
  ## Why this matters
  
  Using `isinstance()` is preferred because:
  
  1. **Inheritance support**: `isinstance(obj, Parent)` returns True for subclasses
  2. **Multiple types**: `isinstance(obj, (int, float))` checks multiple types
  3. **Pythonic**: Follows Python best practices and PEP 8
  
  ### Example
  ```python
  # Instead of
  if type(x) == list or type(x) == tuple:
  
  # Use
  if isinstance(x, (list, tuple)):
  ```
```

### 5. Use Tags Effectively

```yaml
tags:
  - performance      # For optimization rules
  - security        # For security improvements
  - readability     # For code clarity
  - django          # Framework-specific
  - pandas          # Library-specific
  - python3.9       # Version-specific
```

### 6. Scope Rules Appropriately

```yaml
paths:
  include:
    - "src/"           # Only production code
    - "app/models/"    # Specific modules
  exclude:
    - "tests/"         # Not in tests
    - "*_legacy.py"    # Skip legacy files
```

### 7. Handle Edge Cases

Consider and test:
- Empty cases (empty lists, strings)
- None values
- Different quote styles
- Multi-line code
- Comments and docstrings

### 8. Avoid Over-Engineering

Don't create rules for:
- Extremely rare patterns
- Highly context-dependent code
- Personal style preferences (unless team-agreed)
- Patterns with many false positives

### 9. Document Limitations

```yaml
explanation: |
  Note: This rule assumes you're using Python 3.6+.
  For older versions, use % formatting instead of f-strings.
```

### 10. Iterate Based on Feedback

- Monitor false positives
- Gather team feedback
- Refine patterns and conditions
- Update tests with new edge cases

## Troubleshooting

### Rule Not Matching

1. Check pattern syntax
2. Verify capture names are consistent
3. Test with simpler patterns
4. Check for typos in the pattern

### Too Many False Positives

1. Add conditions to restrict matches
2. Make patterns more specific
3. Use path exclusions
4. Consider splitting into multiple rules

### Replacement Issues

1. Ensure all captures used in replacement are defined in pattern
2. Check for proper indentation in multi-line replacements
3. Verify syntax of generated code

### Testing Custom Rules

Use Sourcery CLI to test rules:

```bash
# Validate configuration
sourcery review --verbose .

# Test specific file
sourcery review --config .sourcery.yaml path/to/file.py

# See what rules would match
sourcery review --diff path/to/file.py
```

## Conclusion

Custom rules are a powerful way to maintain code quality and share best practices. Start with simple patterns, test thoroughly, and iterate based on real-world usage. Remember that the best rules are those that provide clear value without creating noise.