# Sourcery Configuration Overview

This documentation covers the configuration and customization of Sourcery using the `.sourcery.yaml` configuration file.

## Table of Contents

1. [Configuration File Basics](#configuration-file-basics)
2. [Configuration File Structure](#configuration-file-structure)
3. [Configuration Options](#configuration-options)
4. [Custom Rules](#custom-rules)
5. [Pattern Syntax](#pattern-syntax)
6. [Rule Settings](#rule-settings)

## Configuration File Basics

### File Location

Sourcery looks for configuration files in the following order:
1. User-level configuration settings from your system
2. Project-level configuration settings from your project root directory (`.sourcery.yaml` or `.sourcery.yml`)
3. Directory specified via command line

If you provide a path to a directory, Sourcery will search for `.sourcery.yml` in that directory. By default, Sourcery searches for `.sourcery.yml` in your current path.

**Priority**: Inline configuration > Project configuration > User configuration

### Creating Configuration

You can create a `.sourcery.yaml` file by running:
```bash
sourcery init
```

### Path Resolution

- Paths in the configuration file are relative to the configuration file location by default
- To specify an absolute path, start it with `/`

## Configuration File Structure

The `.sourcery.yaml` file uses YAML syntax and supports the following fields (all optional):

```yaml
# Files and directories to ignore
ignore:
  - tests/
  - .venv/
  - "*.test.py"

# Rule configuration
rule_settings:
  enable:
    - default
  disable:
    - for-append-to-extend
  rule_types:
    - refactoring
    - suggestion
    - comment
  python_version: '3.9'

# Custom rules
rules:
  - id: custom-rule-id
    pattern: pattern_to_match
    description: What this rule does
    replacement: improved_code

# Rule tag groupings
rule_tags:
  performance:
    - avoid-global-variables
    - list-comprehension

# Code quality metrics
metrics:
  quality_threshold: 25.0

# GitHub integration settings
github:
  request_review: author
  sourcery_branch: sourcery/{base_branch}
  ignore_labels:
    - sourcery-ignore

# Clone detection settings  
clone_detection:
  min_lines: 5
  min_duplicates: 2
  identical_clones_only: false

# Proxy settings
proxy:
  url: http://proxy.example.com:8080
  ssl_certs_file: /path/to/cert.pem
  no_ssl_verify: false
```

## Configuration Options

### ignore
List of files and directories to exclude from Sourcery analysis.

**Default**: Ignores common directories like `.git`, `venv`, `.env`, `node_modules`, etc.

**Example**:
```yaml
ignore:
  - tests/
  - "*.test.py"
  - legacy_code/
```

### rule_settings
Configure which rules Sourcery suggests and how they behave.

**Fields**:
- `enable`: List of rule IDs or tags to exclusively suggest (default: `[default]`)
- `disable`: List of rule IDs or tags to never suggest (overrides `enable`)
- `rule_types`: Types of rules to suggest (default: `[refactoring, suggestion, comment]`)
- `python_version`: Minimum Python version (default: `'3.9'`, minimum: `'3.3'`)

### rules
Define custom project-specific rules. See [Custom Rules](#custom-rules) section for details.

### rule_tags
Create custom groupings of rules for easier management.

**Example**:
```yaml
rule_tags:
  performance:
    - avoid-global-variables
    - use-generator
  security:
    - no-eval
    - secure-random
```

### metrics
Configure code quality metrics.

**Fields**:
- `quality_threshold`: Value between 0-100. Functions below this threshold are highlighted (default: 25.0)

### github
Settings specific to the Sourcery GitHub bot.

**Fields**:
- `request_review`: Who to request review from (`author`, `none`, or GitHub username)
- `sourcery_branch`: Branch naming pattern
- `ignore_labels`: List of labels that prevent Sourcery from analyzing PRs

### clone_detection
Configure Sourcery's code duplication detection.

**Fields**:
- `min_lines`: Minimum lines for clone detection (default: 5)
- `min_duplicates`: Minimum number of duplicates (default: 2)
- `identical_clones_only`: Only detect exact matches (default: false)

### proxy
Configure proxy settings for Sourcery.

**Fields**:
- `url`: Proxy server URL
- `ssl_certs_file`: Path to SSL certificate file
- `no_ssl_verify`: Disable SSL verification (not recommended)

## Custom Rules

Custom rules allow you to define project-specific code patterns and their improvements.

### Rule Structure

A custom rule consists of the following fields:

**Required Fields**:
- `id`: Unique identifier for the rule
- `pattern`: Code pattern to match
- `description`: Brief description of what the rule does

**Optional Fields**:
- `replacement`: Code to replace the matched pattern
- `explanation`: Detailed explanation (supports Markdown)
- `condition`: Boolean condition to restrict matches
- `tags`: List of tags for grouping
- `paths`: Where to apply the rule (include/exclude paths)
- `tests`: Test cases to validate the rule

### Basic Example

```yaml
rules:
  - id: use-isinstance
    description: Use isinstance() instead of type() comparison
    pattern: type(${obj}) == ${type}
    replacement: isinstance(${obj}, ${type})
```

### Advanced Example

```yaml
rules:
  - id: no-debug-logs
    pattern: |
      log.debug(${message})
    description: Remove debug log statements
    replacement: ""
    explanation: |
      Debug logs should not be present in production code.
      They can:
      - Expose sensitive information
      - Impact performance
      - Clutter log files
    tags:
      - production
      - logging
    paths:
      include:
        - "src/"
      exclude:
        - "src/debug/"
    tests:
      - match: log.debug("User data: {user}")
      - no-match: log.info("Application started")
```

### Using Captures

Captures allow you to match and reuse code elements:

```yaml
rules:
  - id: simplify-file-open
    pattern: open(${file}, "r")
    replacement: open(${file})
    description: Files open in read mode by default
```

## Pattern Syntax

Sourcery uses a specialized pattern syntax for matching code.

### Basic Elements

1. **Exact Matches**: Any valid Python code
   ```yaml
   pattern: print("Hello")
   ```

2. **Single Captures**: `${variable_name}`
   ```yaml
   pattern: print(${message})
   ```

3. **Multiple Captures**: 
   - `${var*}`: Zero or more expressions
   - `${var+}`: One or more expressions
   ```yaml
   pattern: print(${args*})
   ```

4. **Wildcards**: `...` matches any code without capturing
   ```yaml
   pattern: def ${name}(...): ...
   ```

5. **Missing Code**: `!!!` matches when optional code is absent
   ```yaml
   pattern: |
     def ${func}(${params*}) -> !!!:
         ...
   ```

### Lenient Matching

Sourcery uses lenient matching, ignoring:
- Decorators (unless specified)
- Return type annotations (unless specified)
- Docstrings (unless specified)
- `async`/`await` keywords (unless specified)
- Type annotations in assignments

### Complex Pattern Example

```yaml
pattern: |
  class ${cls}(${bases*}):
      """!!!"""
      def __init__(self, ${params*}):
          ${body+}
```

This matches classes without docstrings that have an `__init__` method.

## Rule Settings

Rule settings control which Sourcery rules are active and how they behave.

### Configuration Example

```yaml
rule_settings:
  # Only enable specific rules/tags
  enable:
    - default
    - performance
  
  # Disable specific rules (overrides enable)
  disable:
    - for-append-to-extend
    - complex-comprehension
  
  # Types of rules to suggest
  rule_types:
    - refactoring
    - suggestion
    # - comment  # Disabled
  
  # Target Python version
  python_version: '3.8'
```

### Rule Types

1. **refactoring**: Code improvements that don't change behavior
2. **suggestion**: Best practice recommendations
3. **comment**: Add helpful comments to code

### Finding Rule IDs

To find rule IDs to enable/disable:
1. Hover over a Sourcery suggestion in your IDE
2. Check Sourcery's rule documentation
3. Look at the suggestion message which often includes the rule ID

## Best Practices

1. **Start Simple**: Begin with basic rules and gradually add complexity
2. **Test Your Rules**: Always include test cases to validate behavior
3. **Use Tags**: Group related rules for easier management
4. **Document Well**: Use clear descriptions and explanations
5. **Version Control**: Commit your `.sourcery.yaml` to share rules with your team
6. **Iterate**: Refine rules based on team feedback and false positives

## Troubleshooting

### Common Issues

1. **Rule Not Triggering**:
   - Check pattern syntax
   - Verify paths configuration
   - Ensure rule is enabled in `rule_settings`

2. **Too Many False Positives**:
   - Add conditions to restrict matches
   - Use more specific patterns
   - Exclude certain paths

3. **Configuration Not Loading**:
   - Check file name (`.sourcery.yaml` or `.sourcery.yml`)
   - Verify YAML syntax
   - Check file location

### Validation

Run Sourcery with your configuration to validate:
```bash
sourcery review --config .sourcery.yaml .
```

## Additional Resources

- [Sourcery Documentation](https://docs.sourcery.ai/)
- [Pattern Syntax Reference](https://docs.sourcery.ai/Coding-Assistant/Reference/Rules-and-In-Line-Suggestions/Custom-Rules/pattern-syntax/)
- [Custom Rule Examples](https://github.com/sourcery-ai/sourcery-rules)