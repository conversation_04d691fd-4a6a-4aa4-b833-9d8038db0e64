# Sourcery CLI Documentation

## Overview

Sourcery is an AI-powered code quality tool that automatically refactors Python code to make it cleaner and more Pythonic. The CLI requires a Team subscription.

## Installation

Install Sourcery CLI using pip:

```bash
pip install sourcery-cli
```

## Authentication

Login to Sourcery (requires Team subscription):

```bash
# Interactive login
sourcery login

# Token-based login (for CI/CD)
sourcery login --token $SOURCERY_TOKEN
```

## Basic Usage

### Display Refactoring Suggestions

```bash
# Show suggestions for a file or directory
sourcery refactor path/to/file.py
sourcery refactor path/to/directory/

# Refactor code passed as a string
sourcery refactor --code "x = 1 if condition else 2"
```

### Apply Refactorings

```bash
# Apply changes directly to files
sourcery refactor --in-place path/to/file.py

# Apply to multiple files
sourcery refactor --in-place src/ tests/
```

## Command Line Options

### Main Command: `sourcery refactor`

```bash
sourcery refactor [OPTIONS] [SRC]...
```

**Key Options:**

- `-c, --code TEXT`: Refactor code passed as a string
- `--check`: Return exit code 1 if refactorings are found (useful for CI)
- `--diff / --no-diff`: Output a diff for each file with suggested changes
- `--in-place`: Apply changes directly to files
- `-h, --help`: Show help message and exit

### Code Review Command

```bash
# Review code with specific rule sets
sourcery review --enable google-python-style-guide .

# Use multiple rule sets
sourcery review --enable default --enable google-python-style-guide .

# Review only changed code
sourcery review --diff origin/main .
```

## Configuration

### Configuration File

Sourcery reads settings from `.sourcery.yaml` in the project root:

```yaml
# Example .sourcery.yaml
rules:
  - default
  - google-python-style-guide

python_version: "3.9"

exclude:
  - "venv/"
  - "build/"
  - "*_test.py"
```

### Initialize Configuration

```bash
# Set up Sourcery with pre-commit hooks
sourcery init
```

## Integration Examples

### Git Integration

Check only changed files:

```bash
# Check files changed from master
git diff master --name-only | xargs sourcery refactor --check

# Review changes in current branch
sourcery review --diff origin/main .
```

### Pre-commit Hook

Add to `.pre-commit-config.yaml`:

```yaml
repos:
  - repo: https://github.com/sourcery-ai/sourcery
    rev: v0.8.0  # Use latest version
    hooks:
      - id: sourcery
        args: [--diff=origin/main]  # Optional: only check changed files
```

### Continuous Integration

Example CI script:

```bash
#!/bin/bash
# Install Sourcery
pip install sourcery-cli

# Login with token
sourcery login --token $SOURCERY_TOKEN

# Check changed files
git diff origin/main --name-only | xargs sourcery refactor --check

# Exit with non-zero if refactorings found
if [ $? -ne 0 ]; then
    echo "Sourcery found code improvements needed"
    exit 1
fi
```

### GitHub Actions Example

```yaml
name: Sourcery Code Review

on: [pull_request]

jobs:
  sourcery:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
          python-version: '3.9'
      
      - name: Install Sourcery
        run: pip install sourcery-cli
      
      - name: Run Sourcery
        env:
          SOURCERY_TOKEN: ${{ secrets.SOURCERY_TOKEN }}
        run: |
          sourcery login --token $SOURCERY_TOKEN
          sourcery refactor --check --diff origin/${{ github.base_ref }} .
```

## Rule Categories

Sourcery includes 160+ rules divided into three categories:

1. **Refactorings**: Automatically applied changes that don't alter behavior
2. **Suggestions**: Best practice recommendations that may change behavior
3. **No-replacement Rules**: Code quality issues flagged without automatic fixes

## Inline Configuration

Skip Sourcery for specific code:

```python
# Skip all rules for a function
def complex_function():  # sourcery skip
    pass

# Skip specific rule
def another_function():  # sourcery skip: use-fstring
    return "Hello " + name
```

## Advanced Usage

### Custom Rules

Create custom refactoring rules in `.sourcery.yaml`:

```yaml
rules:
  - id: no-print-statements
    pattern: print(...)
    description: Avoid print statements in production code
    replacement: logger.debug(...)
```

### Rule Settings

Configure which rules to enable/disable:

```yaml
rule_settings:
  enable: 
    - default
    - google-python-style-guide
  disable:
    - require-parameter-annotation
    - require-return-annotation
  rule_types:
    - refactoring
    - suggestion
    - comment
```

## Best Practices

1. **Start with `--check` mode** in CI to identify improvements without breaking builds
2. **Review diffs carefully** before applying `--in-place` changes
3. **Use incremental adoption** with `--diff` to review only new code
4. **Configure `.sourcery.yaml`** to match your team's coding standards
5. **Integrate with pre-commit** for automatic checks before commits

## Troubleshooting

### Common Issues

1. **Authentication fails**: Ensure you have a Team subscription
2. **No refactorings found**: Check file extensions and Python version compatibility
3. **Rules not applying**: Verify `.sourcery.yaml` configuration and rule IDs

### Getting Help

- Documentation: https://docs.sourcery.ai
- GitHub Issues: https://github.com/sourcery-ai/sourcery/issues
- Support: <EMAIL>

## Additional Resources

- [Complete Rule Reference](https://docs.sourcery.ai/Reference/Rules-and-In-Line-Suggestions/Python/Default-Rules/)
- [Custom Rules Guide](https://docs.sourcery.ai/Tutorials/Custom-Rules/)
- [VS Code Extension](https://marketplace.visualstudio.com/items?itemName=sourcery.sourcery)