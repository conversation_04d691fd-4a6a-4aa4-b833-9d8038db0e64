# UV Package Manager Documentation

## Overview
UV is an extremely fast Python package and project manager, written in Rust by Astral (makers of Ruff). It's designed to replace pip, pip-tools, pipenv, poetry, pyenv, virtualenv, and more.

## Installation
```bash
# macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# Or with pip
pip install uv

# Or with Homebrew
brew install uv
```

## Project Configuration

### pyproject.toml Structure
```toml
[project]
name = "pfc"
version = "0.0.1"
description = "Your project description"
readme = "README.md"
authors = [{ name = "<PERSON>", email = "<EMAIL>" }]
requires-python = ">=3.12"
dependencies = [
    "fastapi>=0.115.12",
    "pydantic>=2.11.7",
    # ... other deps
]

[dependency-groups]
dev = [
    "ruff",
    "pytest",
    # ... dev deps
]

[tool.uv.sources]
# For local package development
# package-name = { path = "../local-package", editable = true }
```

## Common Commands

### Project Setup
```bash
# Create new project
uv init

# Install dependencies
uv sync

# Install with dev dependencies
uv sync --dev

# Install specific dependency group
uv sync --group dev
```

### Virtual Environment
```bash
# UV automatically creates .venv
# Activate manually if needed
source .venv/bin/activate  # Unix
.venv\Scripts\activate     # Windows
```

### Dependency Management
```bash
# Add dependency
uv add fastapi

# Add dev dependency
uv add --dev pytest

# Add with version constraint
uv add "pydantic>=2.0"

# Remove dependency
uv remove fastapi

# Update dependencies
uv sync --refresh

# Update specific package
uv add "fastapi>=0.115.12" --refresh
```

### Running Commands
```bash
# Run in venv
uv run python script.py
uv run pytest

# Install and run tools
uv tool run ruff check .
```

### Package Building
```bash
# Build package
uv build

# Publish to PyPI
uv publish
```

## Lock File
UV creates `uv.lock` which:
- Pins exact versions
- Ensures reproducible installs
- Should be committed to git

## Performance Features
- Parallel downloads
- Global cache (deduplicates packages)
- Fast resolver written in Rust
- Automatic Python installation

## Integration with Our Project

### Justfile Commands
```makefile
# Install dependencies
install:
    uv sync --dev

# Update dependencies
update:
    uv sync --refresh

# Add new dependency
add pkg:
    uv add {{pkg}}
```

### CI/CD
```yaml
# GitHub Actions
- name: Install UV
  run: curl -LsSf https://astral.sh/uv/install.sh | sh

- name: Install dependencies
  run: uv sync --dev

- name: Run tests
  run: uv run pytest
```

## Configuration Options

### Tool Sources
```toml
[tool.uv.sources]
# Git dependencies
my-package = { git = "https://github.com/user/repo.git" }

# Local path
my-package = { path = "../my-package", editable = true }

# URL
my-package = { url = "https://example.com/package.whl" }
```

### Index Configuration
```toml
[[tool.uv.index]]
name = "private-pypi"
url = "https://private-pypi.com/simple/"
default = true
```

## Best Practices

1. **Always use uv sync** instead of pip install
2. **Commit uv.lock** for reproducible builds
3. **Use .venv** (not venv) for virtual environments
4. **Run commands with uv run** to ensure venv is used
5. **Use dependency groups** to separate dev/test/doc deps

## Troubleshooting

### Cache Issues
```bash
# Clear cache
uv cache clean

# Show cache info
uv cache info
```

### Lock File Conflicts
```bash
# Regenerate lock file
rm uv.lock
uv sync
```

### Python Version Issues
```bash
# Install specific Python
uv python install 3.12

# List available Pythons
uv python list
```

## Advantages Over pip/poetry

1. **Speed**: 10-100x faster than pip
2. **Single tool**: Replaces multiple tools
3. **Better resolver**: Handles conflicts better
4. **Built-in venv**: No separate virtualenv needed
5. **Reproducible**: Lock file ensures consistency
6. **Space efficient**: Global cache deduplicates

## Migration from pip

```bash
# From requirements.txt
uv pip compile requirements.txt -o requirements.txt
uv pip sync requirements.txt

# Better: convert to pyproject.toml
uv init
# Then manually add dependencies to pyproject.toml
uv sync
```