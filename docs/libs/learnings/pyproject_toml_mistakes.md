# pyproject.toml Mistakes & Learnings

## TOML Structure Errors

### Mistake: Wrong Section Nesting
```toml
# WRONG - ignore at wrong level
[tool.ruff.lint.pylint]
ignore = ["S101"]  # This belongs under [tool.ruff.lint]
```

**Fix**: Each tool section has specific fields
```toml
[tool.ruff.lint]
ignore = ["S101"]  # Correct location

[tool.ruff.lint.pylint]  
max-statements = 50  # Pylint-specific settings here
```

### Mistake: Duplicate Keys
```toml
# WRONG - causes parse error
[tool.ruff]
line-length = 150
"*.ipynb" = ["E402"]  # Can't mix settings and per-file ignores
```

**Fix**: Use proper subsections
```toml
[tool.ruff]
line-length = 150

[tool.ruff.lint.per-file-ignores]
"*.ipynb" = ["E402"]
```

## Field Placement Errors

### Mistake: indent-width in wrong section
```toml
# WRONG
[tool.isort]
indent-width = 4  # isort doesn't use this
```

**Fix**: Put in correct tool section
```toml
[tool.ruff]
indent-width = 4  # Or under [tool.ruff.format]
```

## Missing Configurations

### Mistake: No target-version
Without target-version, Ruff might not use Python 3.12 features.

**Fix**: Add target version
```toml
[tool.ruff]
target-version = "py312"  # Match requires-python
```

## Parse Error Patterns

### Error: "unknown field `ignore`"
**Cause**: `ignore` placed under wrong section (like `[tool.ruff.lint.pylint]`)
**Fix**: Move to `[tool.ruff.lint]`

### Error: "unknown field `*.ipynb`"  
**Cause**: File patterns directly under `[tool.ruff]`
**Fix**: Use `[tool.ruff.lint.per-file-ignores]`

## Quick Debugging Tips

1. **Validate TOML syntax**: Use online TOML validator
2. **Check section hierarchy**: tool → ruff → lint → subsection
3. **Read error line numbers**: TOML errors show exact line
4. **One field per line**: Don't mix different config types

## Ruff Configuration Hierarchy

```
[tool.ruff]                    # Basic settings
├── line-length
├── target-version
├── indent-width
├── extend-exclude
│
├── [tool.ruff.lint]           # Linting rules
│   ├── select
│   ├── ignore  
│   ├── [tool.ruff.lint.per-file-ignores]
│   ├── [tool.ruff.lint.pylint]
│   ├── [tool.ruff.lint.mccabe]
│   └── [tool.ruff.lint.isort]
│
└── [tool.ruff.format]         # Formatting options
    ├── quote-style
    ├── indent-style
    └── line-ending
```

## Common Gotchas

1. **D203/D211 conflicts**: These are mutually exclusive - ignore one
2. **D212/D213 conflicts**: Same - pick one style
3. **Preview rules**: Some rules like PLR0904 need preview mode
4. **Case sensitivity**: Rule codes are uppercase (PLR0915, not plr0915)

## Recovery Steps

When pyproject.toml breaks:
1. Check git diff to see recent changes
2. Revert to last working version
3. Apply changes incrementally
4. Run `ruff check .` after each change
5. Watch for parse errors in VS Code