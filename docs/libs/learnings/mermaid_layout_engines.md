**Based on the search results, here are the layout engine options for Mermaid flowcharts:**

**Layout Engines Available**

**• ****Dagre** - Default layout engine for flowcharts, handles hierarchical layouts well and provides good automatic positioning** **

**• ****ELK (Eclipse Layout Kernel)** - Alternative layout engine that can be configured for flowcharts, offers different positioning algorithms and strategies** **

**• ****Cytoscape** - Graph theory/network library used as a layout engine option for some diagram types** **

**Configuration Options**

**• ****Default Renderer Configuration** - Can be set using **defaultRenderer** in flowchart configuration to specify which layout engine to use** **

**• ****ELK Configuration** - Can be configured with specific options like **mergeEdges: true** and **nodePlacementStrategy: LINEAR_SEGMENTS**

**• ****Layout Algorithm Selection** - The **layout** property in configuration defines which layout algorithm to use for rendering diagrams** **

**Usage Notes**

**• Dagre layout works better for hierarchical diagrams, while ELK provides different algorithmic approaches for node positioning**

**• <PERSON><PERSON> is working to decouple layout engines/algorithms to provide more flexibility in choosing rendering approaches **[^1^](https://github.com/mermaid-js/mermaid/issues/5367)
