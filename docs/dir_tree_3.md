# dir tree 3

Updated structure after refactoring - simplified naming with intuitive directory names and file prefixes.

Below is the **updated "Duplo-alphabetised" repo skeleton** that:

* mirrors every non-parallelogram box in the flow-chart,
* adds `iteration_machine_1/2/3` at the exact stages,
* uses simplified directory names that naturally sort in pipeline order,
* prefixes files with parent directory's first letter (c1_, d1_, e1_, etc.),
* installs a **#CLAUDE.md** explainer in every importable package,
* sticks to **pip + uv** (no Poetry), one **top-level `pyproject.toml`**,
* leaves room for adapters, UI harnesses, notebooks, and scratch work.

```text
.                                         # — repo root — -------------------------------------------------
├── pyproject.toml                        # single file for deps/ruff/mypy; uv will read this (no Poetry)
├── README.md                             # quick-start, Mermaid flow, naming rationale
├── .gitignore                            # ignore .tmp/, __pycache__, .venv, *.log …
├── justfile                              # task automation with just
├── .vscode/                              # shared VSCode configuration
│   ├── extensions.json                   # recommended extensions list
│   └── settings.json                     # workspace settings
├── notebooks/                            # exploratory Jupyter work (never imported)
│   └── 2025-07-pipeline-scratch.ipynb
├── .tmp/                                 # throw-away artefacts; always git-ignored
└── src/                                  # the ONLY path added to PYTHONPATH (via "src/ pattern")
    └── pfc/                              # root *runtime* package (prompt flow compiler), holds all stages
        ├── __init__.py                   # keeps pfc/ importable
        ├── #CLAUDE.md                    # describes the whole project in 8-10 lines
        │
        │  # ── CORE: cross-cutting core (models, pipeline, utils) ───────────────────────────────────
        ├── core/                         # shared infrastructure (sorts first alphabetically)
        │   ├── __init__.py
        │   ├── #CLAUDE.md                # explains why core exists & how to extend
        │   ├── c1_models.py              # Pydantic types shared by ≥2 stages
        │   ├── c2_prompts.py             # PROMPTS singleton (micro/sys/usr fields)
        │   ├── c3_pipeline.py            # functional orchestrator + IterationMachine registry
        │   └── utils/                    # helper code that NEVER touches business logic
        │       ├── __init__.py
        │       ├── c4_cli.py             # `python -m pfc.core.utils.c4_cli run examples/config.json`
        │       ├── c5_display.py         # markdown & ANSI render helpers
        │       ├── c6_token_count.py     # thin tiktoken wrapper
        │       ├── scripts/              # utility scripts
        │       │   └── compile_requirements.sh
        │       ├── adapters/             # glue into Kedro, Kestra, LangSmith … optional
        │       │   ├── __init__.py
        │       │   └── c7_kedro_kestra_adapter.py
        │       └── ui/                   # manual testing harnesses
        │           ├── __init__.py
        │           ├── c8_gradio_harness.py
        │           └── c9_streamlit_harness.py
        │
        │  # ── DOC_GEN: Core-Generation stage group ───────────────────────────────────────
        ├── doc_gen/                      # document generation stage
        │   ├── __init__.py
        │   ├── #CLAUDE.md
        │   ├── d1_prompt_generator.py          # maps to "Prompt Generator" box
        │   ├── d2_test_case_generator.py       # "Test Case Generator"
        │   ├── d3_requirements_doc_generator.py# "Requirements Doc Generator"
        │   ├── d4_iteration_machine_1.py       # loops prompt-only; callable from pipeline
        │   └── synthetic_pipeline/             # fan-out sub-dir
        │       ├── __init__.py
        │       ├── #CLAUDE.md
        │       └── d5_synthetic_test_generator.py
        │
        │  # ── EVALUATE: Testing & Evaluation ─────────────────────────────────────────────
        ├── evaluate/                     # test execution and grading
        │   ├── __init__.py
        │   ├── #CLAUDE.md
        │   ├── e1_test_case_chunker.py         # "Test Case Chunker"
        │   ├── e2_test_executor.py             # "Test Executor"
        │   ├── e3_grader_generator.py          # "Grader Generator"
        │   └── e4_test_grader.py               # "Test Grader"
        │
        │  # ── OPTIMIZE: Optimisation Pipeline ───────────────────────────────────────────
        ├── optimize/                     # token optimization stage
        │   ├── __init__.py
        │   ├── #CLAUDE.md
        │   ├── o1_token_optimizer.py           # "Token Optimizer"
        │   ├── o2_final_prompt_generation.py   # "Final Prompt Generation"
        │   └── o3_iteration_machine_3.py       # loops on graders for optimisation
        │
        │  # ── REFINE: Refinement Pipeline ─────────────────────────────────────────────
        ├── refine/                       # feedback and refinement stage
        │   ├── __init__.py
        │   ├── #CLAUDE.md
        │   ├── r1_feedback_summary.py          # "Feedback Summary"
        │   ├── r2_prompt_refiner.py            # "Prompt Refiner"
        │   └── r3_iteration_machine_2.py       # loops on TC outputs vs expected
        │
        │  # ── SHIP: Final Delivery ─────────────────────────────────────────────────
        ├── ship/                         # final packaging and delivery
        │   ├── __init__.py
        │   ├── #CLAUDE.md
        │   └── s1_final_delivery.py            # bundles Final Prompt, TC doc, Req doc
        │
        │  # ── TASKS: Aux - tasks & agent log ─────────────────────────────────────────
        ├── tasks/
        │   ├── __init__.py
        │   ├── #CLAUDE.md
        │   └── ztasks.md                       # rolling agent/LLM to-do & done log
        │
        └── # end pfc/
```

### Key Changes from dir_tree_2

* **Package name**: `p/` → `pfc/` (more descriptive)
* **Directory names**: Simplified from prefixed names to intuitive names:
  - `aa_core/` → `core/`
  - `bb_core_generation/` → `doc_gen/`
  - `cc_testing_evaluation/` → `evaluate/`
  - `dd_refinement/` → `refine/` (moved after optimize to maintain flow)
  - `ee_optimisation/` → `optimize/`
  - `ff_final_delivery/` → `ship/`

* **File naming**: All Python files now use parent directory's first letter:
  - `core/`: c1_models.py, c2_prompts.py, c3_pipeline.py
  - `doc_gen/`: d1_prompt_generator.py, d2_test_case_generator.py
  - `evaluate/`: e1_test_case_chunker.py, e2_test_executor.py
  - `optimize/`: o1_token_optimizer.py, o2_final_prompt_generation.py
  - `refine/`: r1_feedback_summary.py, r2_prompt_refiner.py
  - `ship/`: s1_final_delivery.py

### Benefits of New Structure

* **Natural sorting**: Directories sort alphabetically in pipeline order without prefixes
* **Intuitive names**: Clear, concise directory names that immediately convey purpose
* **Consistent prefixes**: File prefixes make stage association crystal clear
* **Shorter paths**: Easier to type and reference in imports
* **Maintained principles**: Still follows all Duplo-block rules and architectural guidelines

### Commentary & Rules (Updated)

* **File prefixes** use parent directory's first letter + number for ordering within stage
* **Directory names** chosen to naturally sort in pipeline execution order
* **`#CLAUDE.md`** remains in each package for documentation
* **Iteration Machines**:
  * `d4_iteration_machine_1.py` loops on the prompt only
  * `r3_iteration_machine_2.py` fires after grades → feeds `r2_prompt_refiner.py`
  * `o3_iteration_machine_3.py` tweaks optimised prompt based on grader feedback
* **Pydantic model placement** unchanged - locality for stage-specific, `c1_models.py` for shared
* **VSCode configuration** now tracked for team-wide consistency
* **justfile** provides task automation with updated paths

This structure achieves the same goals with cleaner, more intuitive naming that developers will find easier to navigate and understand.