Looking at the Ruff linting errors in your codebase, I'll categorize them by type and provide recommendations:

# TO DO

- [ ] Can we automate a bunch of the Sourcery refactorings?
- [ ] Which Ruff unsafe fixed can we automate via script? E.g., standardised formatting of "  #TODO: " on save etc.

## Ruff Error Codes Explained

### **Formatting Issues (Auto-fixable)**

- **W291**: Trailing whitespace
- **W293**: Blank line contains whitespace
- **RET504**: Unnecessary assignment before `return` statement
- **UP035**: Deprecated typing imports (`Dict`, `Type` → `dict`, `type`)
- **F401**: Imported but unused
- **F841**: Local variable assigned but never used
- **SIM102**: Use single `if` instead of nested `if` statements
- **C417**: Unnecessary `map()` usage (convert to comprehension)
- **C403**: Unnecessary list comprehension (convert to set comprehension)
- **PERF401**: Use `list.extend` instead of loop with `append`
- **RUF005**: Consider iterable unpacking instead of concatenation

### **Code Quality Issues (Consider Fixing)**

- **ANN001/201/202/204**: Missing type annotations
- **ANN003**: Missing type annotation for `**kwargs`
- **ANN401**: Dynamically typed expressions (`Any`) disallowed
- **PLR0911**: Too many return statements (>6)
- **PLR0913**: Too many arguments in function definition (>5)
- **PLR2004**: Magic value used in comparison
- **C901**: Function is too complex
- **PLR1714**: Consider merging multiple comparisons
- **TID252**: Prefer absolute imports over relative imports
- **N802**: Function name should be lowercase

### **Exception Handling Issues**

- **BLE001**: Do not catch blind exception: `Exception`
- **S110**: `try`-`except`-`pass` detected
- **TRY203**: Remove exception handler; error is immediately re-raised

### **Security/Best Practice Issues**

- **S101**: Use of `assert` detected
- **S603**: `subprocess` call: check for execution of untrusted input
- **S607**: Starting a process with a partial executable path
- **T201**: `print` found (should use logging)
- **PTH123**: `open()` should be replaced by `Path.open()`
- **DTZ005**: `datetime.datetime.now()` without timezone

### **Character/Documentation Issues**

- **RUF001/002/003**: Ambiguous characters (EN DASH vs HYPHEN)
- **TD004**: Missing colon in TODO comments
- **INP001**: File is part of implicit namespace package

### **Special Cases**

- **SLF001**: Private member accessed
- **ARG002**: Unused method argument
- **B005**: Using `.strip()` with multi-character strings
- **PLW0127**: Self-assignment of variable
- **RUF012**: Mutable class attributes need `ClassVar`
- **E402**: Module level import not at top of cell
- **F811**: Redefinition of unused function
- **PLW2901**: Loop variable overwritten

## Recommendations

### **1. Auto-fix These (Safe)**

```toml
# In your ruff.toml or pyproject.toml
[tool.ruff]
fix = true
unsafe-fixes = false

# These are safe to auto-fix
fixable = [
    "W291", "W293",  # Whitespace issues
    "F401", "F841",  # Unused imports/variables
    "UP035",         # Type hint updates
    "RET504",        # Unnecessary assignments
    "C417", "C403",  # Comprehension improvements
    "PERF401",       # Performance improvements
]
```

### **2. Disable These (Based on Your Project Style)**

```toml
# Given your brittle code philosophy
ignore = [
    "S101",    # Assert statements are fine for brittle code
    "BLE001",  # Blind exceptions - you want to fix these manually
    "TRY203",  # Re-raising exceptions
    "T201",    # Print statements (if using rich prints)
    "E402",    # Import order in notebooks
    "INP001",  # Implicit namespace (scripts don't need __init__.py)
    "ANN",     # Type annotations (if not using strict typing)
    "TD004",   # TODO format (personal preference)
    "PLR0913", # Too many arguments (if justified)
]
```

### **3. Auto-fix on Save Script**

Create a pre-commit hook or save script:

```python
#!/usr/bin/env python3
# .git/hooks/pre-commit or save_linter.py

import subprocess
import sys

# Safe auto-fixes only
SAFE_FIXES = [
    "W291", "W293",  # Whitespace
    "F401", "F841",  # Unused
    "UP035",         # Type updates
    "RET504",        # Return cleanup
    "RUF001", "RUF002", "RUF003",  # Character fixes
]

# Run ruff with specific fixes
result = subprocess.run([
    "ruff", "check", "--fix", 
    "--select", ",".join(SAFE_FIXES),
    "."
], capture_output=True)

if result.returncode != 0:
    print("Ruff auto-fix failed")
    sys.exit(1)
```

### **4. Manual Review Required**

These should be reviewed case-by-case:

- **BLE001**: Blind exception catching (violates brittle code principle)
- **S603/S607**: Security issues with subprocess
- **PLR2004**: Magic numbers (should be constants)
- **DTZ005**: Timezone-aware datetime (important for correctness)
- **SLF001**: Private member access (code smell)

### **5. Project-Specific Configuration**

Based on your codebase patterns:

```toml
[tool.ruff]
# Your project uses assertions liberally
extend-select = ["S101"]
ignore = ["S101"]

# You use rich prints instead of logging
ignore = ["T201"]

# Notebooks have different import rules
[tool.ruff.per-file-ignores]
"*.ipynb" = ["E402", "F811"]
"scripts/*" = ["INP001"]
".archive/*" = ["ALL"]  # Ignore archived code

# Set complexity thresholds
[tool.ruff.mccabe]
max-complexity = 15  # Increase from default 10

[tool.ruff.pylint]
max-args = 7  # Increase from default 5
max-returns = 8  # Increase from default 6
```

The key is balancing automated fixes with your "brittle code" philosophy - auto-fix formatting issues but manually review logic and error handling changes.
