flowchart TD
    %% ───────────────────────── Input Layer ─────────────────────────
    subgraph IL["Input Layer"]
        direction TB
        PDesc["Problem Description"]
        PProps["problemDesc: string, targetModel: string, tokenLimits: object, constraints: string[]"]
        PDesc --> <PERSON>rops
    end

    %% ──────────────────── Core Generation Pipeline ───────────────────
    subgraph CGP["Core Generation Pipeline"]
        direction TB
        %% Primary generators
        PG["Prompt Generator"]
        TCG["Test Case Generator"]
        RDG["Requirements Doc Generator"]
        %% Output objects
        IP["initialPrompt: string, promptTokens: number, promptVersion: number"]
        TCats["testCategories: JSON, categoryDefinitions: object[], templateCount: number"]
        RDoc["requirementsDoc: object, workflowExpectations: object, qualityMetrics: object"]

        PG --> IP
        TCG --> TCats
        RDG --> R<PERSON><PERSON>
        PProps --> <PERSON><PERSON>
        PProps --> TCG
        PProps --> RDG

        %% quality metrics feedback to TCG & Synth pipeline
        RDoc -- qualityMetrics --> TCG

        %% ───── Synthetic Data Pipeline ─────
        subgraph SDP["Synthetic Data Pipeline"]
            direction TB
            STG["Synthetic Test Generator"]
            STObj["syntheticTest: object[], testInputs: string[], expectedOutputs: object"]
            STG --> STObj
        end
        TCG --> SDP
    end

    %% ─────────────────── Testing & Evaluation ───────────────────
    subgraph TE["Testing & Evaluation"]
        direction TB
        TChunk["Test Case Chunker"]
        ChunkObj["testChunks: object[], chunkCount: number, categoryMap: object"]
        TExec["Test Executor"]
        TResult["testResults: object[], passCount: number, failCount: number, testOutputs: object"]
        TGrader["Test Grader"]
        TGrades["testGrades: object[], passFailSummary: object[], categoryPerformance: object"]
        GGen["Grader Generator"]

        IP --> TChunk
        STObj --> TChunk
        TChunk --> ChunkObj --> TExec
        GGen --> TExec
        TExec --> TResult --> TGrader
        TGrader --> TGrades
        GGen --> TGrader
    end

    %% ───────────────────── Evaluate Prompt Pipeline ─────────────────────
    subgraph RP["Evaluate Prompt Pipeline"]
        direction TB
        FSum["Feedback Summary"]
        IAObj["improvementAreas: object[], clusterSummary: object[], priorityIssues: string[]"]
        PRef["Prompt Refiner"]
        RPrompt["refinedPrompt: string, changeLog: object[], promptVersion: number"]

        TGrades --> FSum --> IAObj --> PRef --> RPrompt
    end

    %% ───────────────────── Optimize from Outputs Pipeline ─────────────────────
    subgraph OP["Optimize from Outputs Pipeline"]
        direction TB
        FSum2["Feedback Summary"]
        IAObj2["improvementAreas: object[], clusterSummary: object[], priorityIssues: string[]"]
        PRef2["Prompt Refiner"]
        RPrompt2["refinedPrompt: string, changeLog: object[], promptVersion: number"]

        RPrompt --> FSum2 --> IAObj2 --> PRef2 --> RPrompt2
    end

    %% ───────────────────── Realign from Requirements Pipeline ─────────────────────
    subgraph RAP["Realign from Requirements Pipeline"]
        direction TB
        TokOpt["Token Optimizer"]
        OptObj["optimizedPrompt: string, tokenReduction: number, optimizationTechniques: string[]"]
        FPG["Final Prompt Generation"]
        FPrompt["finalPrompt: string, finalTokenCount: number, performanceMetrics: object"]

        RPrompt2 --> TokOpt --> OptObj --> FPG --> FPrompt
    end

    %% ──────────────────────── Final Delivery ────────────────────────
    subgraph FD["Final Delivery"]
        direction TB
        FTCDoc["Final Test Case Generation Document"]
        FRPrompt["Final Refined Prompt"]
        FRDoc["Final Requirements Document"]
        FPrompt --> FTCDoc
        FPrompt --> FRPrompt
        FPrompt --> FRDoc
    end

    %% ──────────────── Iteration Machines & Feedback Loops ────────────────
    classDef loop stroke-dasharray: 5 3,stroke-width:1px;

    %% IterationMachine1 – prompt-only loop
    PRef -.->|IterationMachine1| PG
    class PG loop

    %% IterationMachine2 – on TC outputs + expected
    TGrades -.->|IterationMachine2| PRef2
    class PRef2 loop

    %% IterationMachine3 – graders on TC outputs
    TGrader -.->|IterationMachine3| TokOpt
    class TokOpt loop
