# Architecture Rules 2 Pydantic + Naming Conventions

## DECISIONS (CC)

### A<PERSON> said:

> I like TestSpec. OK, Review
> @docs/architecture_rules_2_pydantic_extra_11jul.md -> What do you think we
> SHOULD take from this and why? They use _Base - should we? Industry
> standard for what we are doing wherever possible. Output, don't edit code.
> ⎿  Read docs/architecture_rules_2_pydantic_extra_11jul.md (480 lines)

✻ Thinking…

  The user wants me to review the architecture rules document and determine
  what we should adopt from it for our PFC pipeline, particularly focusing
  on:

1. The _Base pattern
2. Industry standard naming conventions
3. Immutability patterns

  Key takeaways from the document:

1. Clear naming hierarchy: Spec → Run → Result → Grade
2. Use of _Base class with common fields (id, created_at) and frozen
   config
3. Tuples for immutable collections (not lists)
4. Doc suffix for human-authored "whole documents"
5. Plural naming with 's' suffix or Tuple[Item, ...] for collections
6. frozen=True and extra='forbid' for strict immutability

  Looking at the current models.py, we already have:

- PFCBaseModel (similar to _Base)
- frozen=True in the config
- extra="forbid"
- Proper inheritance structure

  What we should consider adopting:

1. The Spec → Run → Result → Grade naming pattern for test execution
2. Clear plural naming conventions
3. Maybe rename some classes to better match industry standards
4. Consider using tuples for collections instead of lists for true
   immutability

### Key Decisions from Industry Best Practices

#### 1. Clear Transformation Chain (Industry Standard)

**TestSpec → TestRun → TestResult → TestGrade**

This four-layer transformation is used by OpenAI Evals, Anthropic, and LangSmith:

- **TestSpec**: Immutable task definition (our Scenario with if_this/then_that)
- **TestRun**: Captures prompt, params, latency during execution
- **TestResult**: Raw model response & metadata
- **TestGrade**: Adds evaluator, score, and feedback

#### 2. Naming Conventions

| Pattern              | Usage                            | Example                                      |
| -------------------- | -------------------------------- | -------------------------------------------- |
| **Doc suffix** | Human-authored "whole documents" | TaskBriefDoc, PromptDoc, ReqDoc, ScenarioDoc |
| **No suffix**  | Machine-generated artifacts      | TestResult, TestGrade, FinalPrompt           |
| **Plural 's'** | Type aliases for collections     | TestSpecs = tuple[TestSpec, ...]             |
| **_PFCBM**     | Base class (not _Base)           | Our existing pattern, more descriptive       |

#### 3. Iteration Machine Architecture

| IM             | Takes                   | Emits           | Purpose                          |
| -------------- | ----------------------- | --------------- | -------------------------------- |
| **IM-1** | NorthStar (all docs)    | PromptDoc       | Conceptual refinement (no tests) |
| **IM-2** | TestResults (no grades) | RefinedPrompt   | Performance-based refinement     |
| **IM-3** | TestGrades              | OptimizedPrompt | Efficiency optimization (tokens) |

#### 4. Immutability Patterns

use plural tuples for list Types (eg TestCase vs TestCases) BUT use list

```python
# Build with lists during execution (ergonomic)
results: list[TestResult] = []
for test in tests:
    results.append(run_test(test))

# Convert to tuples at boundaries (immutable)
immutable_results: TestResults = tuple(results)
```

#### 5. Chesterton's Fence Principles

- **Don't collapse layers**: Keep TestResult and TestGrade separate for flexibility
- **Nouns for data, verbs for functions**: `run_prompt()`, `run_grader()` as functions
- **Plural = collection**: Eliminates "is this one item or many?" bugs
- **Version everything**: Enables diff, rollback, and audit trails

#### 6. Benefits of Frozen Objects

```python
# Hashable objects enable caching
cache: dict[TestSpec, TestResults] = {}
if spec not in cache:
    cache[spec] = run_tests(spec)
```

- Immutability forces explicit version bumps
- Prevents accidental prompt drift
- Enables referential transparency
- Makes rollback trivial
