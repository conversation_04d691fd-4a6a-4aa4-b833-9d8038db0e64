[project]
name = "pfc"  # Replace with your project name
version = "0.0.1"  # Semantic versioning
description = "Your project description"  # Brief project description
readme = "README.md"  # Points to project readme file
authors = [{ name = "<PERSON>", email = "<EMAIL>" }]  # Project maintainers
requires-python = ">=3.12"  # Python version requirement
dependencies = [
    "fastapi>=0.115.12",  # Web framework for building APIs
    "uvicorn>=0.34.0",  # ASGI server for FastAPI
    "mcp[cli]>=1.6.0",  # Model Context Protocol CLI tools
    "pydantic>=2.11.7",  # Data validation using Python type annotations
    "aiofiles>=24.1.0",  # Async file operations
    # Agentic
    "openai-agents>=0.0.14",  # OpenAI agents framework
    # NLP
    "rapidfuzz>=3.0.0",  # Fast string matching library
]

# Local development dependencies - uncomment to use local packages
# [tool.uv.sources]
# package-name = { path = "../local-package-path", editable = true }


[dependency-groups]
dev = [
    "pre-commit",  # Git hook scripts for code quality
    "ruff",  # Fast Python linter and formatter
    "ty>=0.0.1a13",  # Type checker for Python
    # Test
    "pytest",  # Testing framework
    "pytest-asyncio",  # Pytest support for asyncio
    "pytest-timeout",  # Timeout plugin for pytest
    "pytest-dotenv",  # Load .env files in pytest
    "pytest-mock",  # Thin wrapper around mock for pytest
    "pytest-cov",  # Coverage plugin for pytest
]

# Configure custom package indexes if needed
# [[tool.uv.index]]
# name = "private-pypi"
# url = "https://your-private-pypi.com/simple/"
# default = true

[build-system]
requires = ["hatchling"]  # Build dependencies
build-backend = "hatchling.build"  # PEP 517 build backend

[tool.hatch.build.targets.wheel]
packages = ["src/your_package_name"]  # Package directory to include in wheel


[tool.ruff]
line-length = 88  # Maximum line length (Black default)
indent-width = 4  # Number of spaces per indentation level

[tool.ruff.lint]
select = ["ALL"]  # Enable all lint rules by default

ignore = [
    "D",  # Ignore all docstring rules
    "COM812",  # Allow trailing comma missing
    "ISC001",  # Allow implicit string concatenation
    "TRY003",  # Allow long messages in exception
    "EM101",  # Allow raw string in exception
    "EM102",  # Allow f-strings in exceptions
    "FIX002",  # Allow TODO comments
    "TD002",  # Allow missing author in TODO
    "TD003",  # Allow missing issue link in TODO
    "E501",  # Allow line too long (handled by formatter)
    "FBT001",  # Allow boolean positional arg in function definition
    "FBT002",  # Allow boolean default value in function definition
    "FBT003",  # Allow boolean positional value in function call
    "TRY300",  # Allow try-except-pass
    "G004",  # Allow logging f-strings
]

[tool.ruff.lint.per-file-ignores]
# Test files - relaxed rules for testing
"**/tests/**/*test*.py" = [
    "ANN001",  # Allow non-typed function arguments
    "ANN002",  # Allow non-typed *args
    "ANN003",  # Allow non-typed **kwargs
    "ARG001",  # Allow unused function arguments
    "ANN201",  # Allow missing return type annotation
    "PLR2004", # Allow magic values in tests
    "S101",    # Allow assert statements in tests
    "S106",    # Allow hard-coded passwords in tests
    "SLF001",  # Allow private member access in tests
    "S105",    # Allow hard-coded passwords in tests
]
# Example files - relaxed rules for examples
"examples/**" = [
    "ANN001",  # Allow non-typed function arguments
    "ANN002",  # Allow non-typed *args
    "ANN003",  # Allow non-typed **kwargs
    "ARG001",  # Allow unused function arguments
    "ANN201",  # Allow missing return type annotation
    "PLR2004", # Allow magic values in examples
    "S101",    # Allow assert statements in examples
    "S106",    # Allow hard-coded passwords in examples
    "SLF001",  # Allow private member access in examples
    "S105",    # Allow hard-coded passwords in examples
    "T201",    # Allow print statements in examples
]


[tool.ruff.lint.isort]
known-third-party = ["fastapi", "pydantic"]  # Third-party imports for sorting

[tool.ruff.format]
quote-style = "double"  # Use double quotes for strings
indent-style = "space"  # Use spaces for indentation
skip-magic-trailing-comma = false  # Respect trailing commas
line-ending = "auto"  # Auto-detect line endings

[tool.ty]
# Type checker configuration - add rules as needed

[tool.pytest.ini_options]
pythonpath = ["."]  # Add current directory to Python path
asyncio_mode = "auto"  # Auto-detect async tests
asyncio_default_fixture_loop_scope = "function"  # New event loop per test
addopts = ["--cov", "-p no:warnings", "-s"]  # Coverage, no warnings, no capture
python_files = ["test_*.py"]  # Test file pattern
markers = ["unit", "integration", "performance", "e2e", "slow"]  # Custom test markers