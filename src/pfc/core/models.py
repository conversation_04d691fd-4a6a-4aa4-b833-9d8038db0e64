"""Core Pydantic models for the PFC pipeline. NOTE: <default-fold-this>

    PYDANTIC ARCHITECTURE RULES (from architecture_rules_1_9jul.md):

    1. **Strict mode everywhere**
    - ConfigDict(strict=True) - but we use arbitrary_types_allowed=True for
        flexibility

    2. **No optional fields in intermediate models**
    - Optional → ambiguity → hidden failures
    - Optional allowed ONLY at system boundaries (CLI, HTTP)

    3. **Validate on assignment**
    - model_config = {"validate_assignment": True}

    4. **Forbid extra keys**
    - extra="forbid" helps catch upstream schema drift instantly

    5. **Custom validators over assert when field-specific**
    - Makes error messages actionable
    - Keep asserts for invariants spanning multiple fields

    6. **Never call .model_dump() without mode="json" or by_alias=True**
    - Reduces accidental type coercion

    7. **Immutable config for inbound config objects**
    - frozen=True guards "Business Logic is Sacred"
    - Once stamped valid, can't mutate unless cloned

    8. **Version every major schema**
    - __schema_version__ = "v1" attribute for diff & migrate

    9. **DocTests in each model's docstring**
    - Show one happy path & one failure; doubles as spec

    10. **Keep each model ≤ 25 lines**
        - Long models hide complexity that belongs in another block

    ADDITIONAL NOTES:
    - If schema used only inside one stage, declare it in that stage's file (locality > DRY)
    - If schema shared across stages/adapters, re-export in models.py for single-hop imports
    - Only schema + validation in pydantic classes
    - Minimised side effects, maximum functional, lean towards immutability
"""

# NOTE: totally incomplete, just placeholder for exploring optimal pydantic architecture. Strict Rules + Minimum LOC + max readability.

from datetime import datetime
from typing import ClassVar, Literal

from pydantic import BaseModel, ConfigDict, Field

from .utils.version import bump_version


class _PFCBM(BaseModel):
    """Base model for PFC models with common Pydantic configurations.
    - enforces the strict types and strict immutabiltiy etc across all models.
    - also enforces the Pydantic architecture rules."""

    model_config = ConfigDict(
        strict=True,
        arbitrary_types_allowed=True,
        validate_assignment=True,
        extra="forbid",
        frozen=True,
    )


class ProblemDescriptionDoc(_PFCBM):
    """DescriptionDoc of the problem/task to solve. Client / DKE generated.
    NOTE: CORE INITIATING SYSTEM INPUT.

    >>> pd = ProblemDescriptionDoc(description="Summarize the text", files=["file1.txt", "file2.txt"], images=["image1.png", "image2.png"])
    >>> pd.description
    'Summarize the text'
    >>> pd.files
    ['file1.txt', 'file2.txt']
    >>> pd.images
    ['image1.png', 'image2.png']
    """

    description: str  # TODO: should this just be a single md or docx or something? For full self-containment?
    files: list[str] | None = None  # TODO: add file types & handle, optional. Should be filepaths?
    images: list[str] | None = None  # TODO: add image types & handle, optional. Should be urls? or B64?


class BasePromptConfig(_PFCBM):
    """Common config shared by all models."""

    model: str  # TODO - add list of Literal model options. comment out those not integrated.

    # Don't use these, here for documentation purposes.
    # top_p: float = 1.0
    # frequency_penalty: float = 0.0
    # presence_penalty: float = 0.0
    # stop: list[str] = Field(default_factory=list)
    # n: int = 1
    # logprobs: int | None = None


class StandardPromptConfig(BasePromptConfig):
    """Config for standard models."""

    temperature: float = 0.1
    max_tokens: int  # TODO - both need max values. Default to max and generally DONT USE THIS


class ReasoningPromptConfig(BasePromptConfig):
    """Config for reasoning models (o1, o3, etc)."""

    max_completion_tokens: int  # reasoning models use this instead of max_tokens
    max_reasoning_tokens: int  # TODO - both need max values. Default to max and generally DONT USE THIS
    reasoning_effort: Literal["low", "medium", "high"] = "low"  # TODO - check,
    # Note: no temperature, top_p, etc. for reasoning models
    temperature: float = 1  # TODO -> some reasoning models DO use temperature.


class Prompt(_PFCBM):
    """Immutable prompt object containing system and user messages.

    >>> p = Prompt(
    ...     oner="Extract key points",
    ...     sys="You are helpful",
    ...     usr="Summarize this...",
    ...     config=StandardPromptConfig(model_name="gpt-4", temperature=0.7, max_tokens=1000)
    ... )
    >>> p.oner
    'Extract key points'
    >>> p.config.temperature
    0.7
    """

    ONER_MAX_LENGTH: ClassVar[int] = 300  # Class-level constant for oner length

    # PromptConfig, e.g., model_name, provider, temp, reasoning model etc
    config: StandardPromptConfig | ReasoningPromptConfig

    # prompt abstractions:
    description: str | None = None  # Optional, does nothing, oai agents sdk uses it. just for documentation.
    oner: str = Field(
        ..., max_length=ONER_MAX_LENGTH
    )  # mandatory ultra-concise single line version of prompt sys+usr for abstraction/documentation purposes.

    # Core Prompt Strings
    sys: str
    usr: str

    # response: str  # NOTE (LOC documentation only)if immutable, can't contain response.


class PromptDoc(_PFCBM):
    """Container for prompt documentation and metadata. Immutable, versioned.

    >>> pd = PromptDoc(
    ...     v="1.0.0",
    ...     prompt=Prompt(
    ...         oner="Summarize text",
    ...         sys="You are helpful",
    ...         usr="Summarize: ...",
    ...         config=StandardPromptConfig(model="gpt-4", temperature=0.7, max_tokens=1000)
    ...     )
    ... )
    >>> pd.v
    '1.0.0'
    >>> pd.next("iter").v
    '1.0.1'
    >>> pd.next("minor").v
    '1.1.0'
    >>> pd.next("major").v
    '2.0.0'
    """

    v: str = "0.0.0"  # Version: major.minor.iteration
    prompt: Prompt

    # Metadata
    created_at: datetime = Field(default_factory=datetime.now)
    parent_v: str | None = None  # Parent version for lineage

    def next(self, bump: Literal["iter", "minor", "major"] = "iter") -> "PromptDoc":
        """Increment version. New instance with parent_v for lineage."""
        return PromptDoc(v=bump_version(self.v, bump), prompt=self.prompt, parent_v=self.v)


class PromptResponse(_PFCBM):
    """Container for LLM responses.

    >>> pr = PromptResponse(response="Here are 3 key points...")
    >>> pr.response
    'Here are 3 key points...'
    >>> PromptResponse(response="text", extra_field="bad")  # doctest: +IGNORE_EXCEPTION_DETAIL
    Traceback (most recent call last):
    ValidationError: Extra inputs are not permitted
    """

    description: str | None = None  # does nothing, just for documentation.
    response: str


class ReqDoc(_PFCBM):
    """Requirements extracted from problem description.

    >>> rd = ReqDoc(
    ...     v="0.0.0",
    ...     requirements=["Must summarize text", "Keep under 100 words"],
    ...     constraints=["No technical jargon", "Maintain tone"],
    ...     source_prompt_doc_v="1.0.0"
    ... )
    >>> rd.requirements[0]
    'Must summarize text'
    """

    v: str = "0.0.0"  # Version tracking
    requirements: list[str]  # Extracted requirements
    constraints: list[str] = Field(default_factory=list)  # Optional constraints
    source_prompt_doc_v: str  # Link to PromptDoc version that generated this

    def next(self, bump: Literal["iter", "minor", "major"] = "iter") -> "ReqDoc":
        """Increment version."""
        return ReqDoc(
            v=bump_version(self.v, bump), requirements=self.requirements, constraints=self.constraints, source_prompt_doc_v=self.source_prompt_doc_v
        )


class Scenario(_PFCBM):
    """Single test scenario."""

    name: str
    if_this: str
    then_that: str
    type: Literal[
        # Basic/Standard test categories
        "regression",  # TCs that are simple cases that should always pass
        "nominal",  # TCs that test expected normal operation
        "baseline",  # TCs that test reference implementation behavior
        "hard",  # TCs that are complex but valid scenarios
        # "stress",  # TCs that test high load or volume scenarios.
        "edge",  # TCs that test rare but legitimate scenarios
        "boundary",  # TCs that explore boundary conditions. Where do we _expect_ model to not know what to do?
        # Retrieval testing - only for retrieval-relevant tasks.
        "faithfulness",  # TCs that test output matches source material
        "answer_relevancy",  # TCs that test response addresses the question
        "contextual_precision",  # TCs that test relevant context is prioritized
        "contextual_recall",  # TCs that test all relevant context is included
        # Accuracy / Handling Ambiguity
        "hallucination",  # TCs that test for false or fabricated information
        "coherence",  # TCs that are written poorly that might lead model to incoherent outputs
        "relevance",  # TCs that could lead model off-topic
        "summarization",  # TCs that are more likely to cause loss of information
        # Shot-based / context-provision variation testing.
        # eg one user gives 0 examples, another a pdf with 100 examples.
        "many_shot",  # TCs that provide multiple examples
        "few_shot",  # TCs that provide limited examples
        "zero_shot",  # TCs that provide no examples, pure instruction following
        "large_context",  # TCs that provide large file context
        # Moderation (safety and bias detection)
        # "bias",  # TCs that test for unfair treatment of groups or topics
        # "toxicity",  # TCs that test for harmful, offensive, or inappropriate content
        # # Security and adversarial testing
        # "adversarial",  # TCs that are designed to exploit weaknesses
        # "jailbreak",  # TCs that attempt to bypass safety constraints
        # "prompt_injection",  # TCs that embed malicious input in prompts
        # "red_team",  # TCs that simulate systematic attacks
        # "encoding_evasion",  # TCs that use obfuscated malicious input
        # "roleplay_attack",  # TCs that use social engineering via character play
    ]  # no default, must be one of the above.


class ScenarioDocChunk(_PFCBM):
    """Chunk of a scenario doc. Split ScenarioDoc ahead of parallel generation.
    (A) TC_Generator(seed, all_seeds_as_context) → TestCase
    (B) TC_Generator(seed, some_seeds_as_context) → TestCase"""

    # NOTE: AF: Not totally clear to me this is necessary vs (A). DD prefers it atm. Jul 10th.

    scenario: Scenario
    chunk_index: int
    chunk_text: str


class ScenarioDoc(_PFCBM):
    """Collection of test scenarios.
    NOTE: AF: Example: src/pfc/core/model.py_examples/ScenarioDoc/example1/scenario_doc_example.json

    >>> sd = ScenarioDoc(
    ...     v="0.0.0",
    ...     scenarios=[Scenario(name="Basic", input="Hello", expected_behavior="Greeting response")],
    ...     source_req_doc_v="1.0.0"
    ... )
    >>> sd.scenarios[0].name
    'Basic'
    """

    v: str = "0.0.0"
    scenarios: list[Scenario]
    source_req_doc_v: str  # Link to ReqDoc version

    def next(self, bump: Literal["iter", "minor", "major"] = "iter") -> "ScenarioDoc":
        """Increment version."""
        return ScenarioDoc(v=bump_version(self.v, bump), scenarios=self.scenarios, source_req_doc_v=self.source_req_doc_v)


class TestResult(_PFCBM):
    """Result from executing a single test."""

    scenario_name: str
    actual_output: str
    execution_time: float
    error: str | None = None


class TestResults(_PFCBM):
    """Collection of test execution results."""

    v: str = "0.0.0"
    results: list[TestResult]
    prompt_doc_v: str  # Which prompt version was tested


class TestGrade(_PFCBM):
    """Grade for a single test result."""

    scenario_name: str
    passed: bool
    score: float = Field(ge=0.0, le=1.0)  # 0-1 score
    feedback: str


class TestGrades(_PFCBM):
    """Collection of graded test results."""

    v: str = "0.0.0"
    grades: list[TestGrade]
    overall_score: float = Field(ge=0.0, le=1.0)
    test_results_v: str  # Link to TestResults version


class FeedbackSummary(_PFCBM):
    """Summarized feedback from test grades."""

    v: str = "0.0.0"
    strengths: list[str]
    weaknesses: list[str]
    suggestions: list[str]
    source_grades_v: str


class RefinedPrompt(_PFCBM):
    """Refined prompt based on feedback."""

    v: str = "0.0.0"
    prompt: Prompt  # The refined prompt
    changes_made: list[str]  # What was changed
    source_feedback_v: str


class OptimizedPrompt(_PFCBM):
    """Token-optimized version of refined prompt."""

    v: str = "0.0.0"
    prompt: Prompt
    token_reduction: float  # Percentage reduced
    source_refined_v: str


class FinalPrompt(_PFCBM):
    """Final production-ready prompt."""

    v: str = "0.0.0"
    prompt: Prompt
    documentation: str  # Usage instructions
    source_optimized_v: str
