---
config:
  layout: elk
---
flowchart TD
 subgraph subGraph0 ["📝 Doc Generation"]
        d1["d1_PromptDoc<br>Generate Initial Prompt"]
        d2["d2_ReqDoc<br>Extract Requirements"]
        d3["d3_ScenarioDoc<br>Generate Test Scenarios"]
        NS1["NorthStar1"]
        d4["d4_iteration_machine_1<br>🔄 Iterate on Docs"]
  end
 subgraph subGraph1["🧪 Test Generation"]
        d5["d5_synthetic_test_generator<br>Generate Synthetic Tests"]
  end
 subgraph subGraph2["⚡ Test Execution"]
        e1["e1_test_case_chunker<br>Chunk Test Cases"]
        e2["e2_test_executor<br>Execute Tests"]
  end
 subgraph subGraph3["📊 Requirements Grader Generation"]
        e3["e3_grader_generator<br>Generate Graders"]
        e4["e4_test_grader<br>Grade Results"]
  end
 subgraph subGraph4["🔧 Optimize from Outputs"]
        r1["r1_feedback_summary<br>Summarize Feedback"]
        r2["r2_prompt_refiner<br>Refine Prompt"]
        r3["r3_iteration_machine_2<br>🔄 Iterate on Refinements"]
  end
 subgraph subGraph5["🎯 Realign from Requirements"]
        o1["o1_token_optimizer<br>Optimize Tokens"]
        o2["o2_final_prompt_generation<br>Generate Final"]
        o3["o3_iteration_machine_3<br>🔄 Iterate on Optimization"]
  end
 subgraph subGraph6["🚀 Ship"]
        s1["s1_final_delivery<br>Package &amp; Deliver"]
  end
    ProblemDesc["📄 ProblemDescriptionDoc<br>Initial Requirements"] -- "v0.0.0" --> d1
    
    d1 -- "PromptDoc v1.0.0" --> d2
    d2 -- "ReqDoc v1.0.0" --> d3
    
    d1 --> NS1
    d2 --> NS1
    d3 --> NS1
    NS1 --> d4
    d4 -- iterate --> d4
    
    d4 -- PromptDoc1 --> d5
    d3 -- ScenarioDoc --> d5
    d5 -- SyntheticTests --> e1
    d1 -- PromptDoc --> e1
    e1 -- TestChunks --> e2
    d2 -- ReqDoc --> e3
    e3 -- Graders --> e2
    e2 -- TestResults --> e4
    e4 -- "TestGrades v1.0.0" --> r1
    r1 -- FeedbackSummary --> r2
    r2 -- "RefinedPrompt v1.0.0" --> r3
    r3 -. iterate .-> r1
    r3 -- RefinedPrompt --> o1
    o1 -- OptimizedPrompt --> o2
    o2 -- "FinalPrompt v1.0.0" --> o3
    o3 -. iterate .-> o1
    o3 -- FinalPrompt --> s1
     d1:::process
     d2:::process
     d3:::process
     d4:::iteration
     d5:::test
     e1:::test
     e2:::test
     e3:::test
     e4:::test
     r1:::optimize
     r2:::optimize
     r3:::iteration
     o1:::optimize
     o2:::optimize
     o3:::iteration
     s1:::ship
     ProblemDesc:::input
    classDef input fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef process fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef test fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef optimize fill:#e8f5e9,stroke:#1b5e20,stroke-width:2px
    classDef ship fill:#ffebee,stroke:#b71c1c,stroke-width:2px
    classDef iteration fill:#fffde7,stroke:#f57f17,stroke-width:3px