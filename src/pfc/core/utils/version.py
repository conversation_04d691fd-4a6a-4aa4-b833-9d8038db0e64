"""Version bumping utilities for PFC models.

This module provides shared version management functionality used across
multiple Pydantic models in the PFC pipeline.
"""

from typing import Literal


def bump_version(version: str, bump: Literal["iter", "minor", "major"] = "iter") -> str:
    """Increment version string (major.minor.iter format).

    Args:
        version: Current version string (e.g., "1.2.3")
        bump: Which part to increment ("major", "minor", or "iter")

    Returns:
        New version string with the specified part incremented

    Examples:
        >>> bump_version("1.2.3", "iter")
        '1.2.4'
        >>> bump_version("1.2.3", "minor")
        '1.3.0'
        >>> bump_version("1.2.3", "major")
        '2.0.0'
    """
    parts = version.split(".")
    idx = {"major": 0, "minor": 1, "iter": -1}[bump]
    if idx == -1:
        idx = len(parts) - 1
    parts[idx] = str(int(parts[idx]) + 1)
    # Reset all positions after the bumped one
    for i in range(idx + 1, len(parts)):
        parts[i] = "0"
    return ".".join(parts)
