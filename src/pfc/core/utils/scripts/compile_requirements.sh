#!/usr/bin/env bash
# Compile requirements.txt from pyproject.toml
# This script generates a pip-compatible requirements.txt file from pyproject.toml
# for users who prefer traditional pip over uv package manager

# Exit on error, undefined variable, or pipe failure
set -euo pipefail

# Get the root directory (5 levels up from this script)
# Script location: src/pfc/core/utils/scripts/compile_requirements.sh
# Root location: ./ (5 levels up)
ROOT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/../../../../../" && pwd)"

# Change to root directory where pyproject.toml is located
cd "$ROOT_DIR"

echo "Compiling requirements.txt from pyproject.toml..."

# Check if pip-compile is available via uv
# pip-compile is part of pip-tools package and converts pyproject.toml to requirements.txt
if ! uv run pip-compile --version &> /dev/null; then
    echo "pip-compile not found. Installing pip-tools..."
    # Add pip-tools as a dev dependency using uv
    uv add --dev pip-tools
fi

# Compile requirements using uv to run pip-compile
# --no-emit-index-url: Don't include index URLs in the output (cleaner file)
# This WILL OVERWRITE any existing requirements.txt file
uv run pip-compile pyproject.toml -o requirements.txt --no-emit-index-url

# Add header comments to the generated file
# First line: warning that this is auto-generated
echo "# Generated from pyproject.toml - do not edit directly" | cat - requirements.txt > temp && mv temp requirements.txt
# Second line: how to regenerate this file
echo "# Run: just compile-deps" >> requirements.txt

echo "✓ requirements.txt compiled successfully"