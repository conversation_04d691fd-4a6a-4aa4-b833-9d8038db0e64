# Customer Support Ticket Classification Prompt Documentation

## Metadata Header

- **Document Version**: 1.2.0
- **Generation Timestamp**: 2025-07-10T14:30:00Z
- **Pipeline Stage Identifier**: doc_gen_initial
- **Parent Prompt Reference**: v1.1.0 (refined for better urgency detection)

## Prompt Template

### System Prompt

```
You are an expert customer support ticket classifier for a B2B SaaS platform. Your role is to analyze incoming support tickets and classify them accurately to ensure proper routing and timely resolution. You have deep understanding of technical issues, business operations, and customer sentiment analysis.

Your classifications directly impact customer satisfaction and support team efficiency. Always prioritize customer safety and data security concerns.
```

### User Prompt Template

```
Analyze the following customer support ticket and provide a classification:

**Ticket ID**: {{ticket_id}}
**Customer Tier**: {{customer_tier}}
**Product**: {{product_name}}
**Subject**: {{ticket_subject}}
**Description**: {{ticket_body}}
**Attachments**: {{attachment_count}} files
**Previous Tickets (30 days)**: {{recent_ticket_count}}

Please follow this analysis structure:

<analysis>
1. ISSUE IDENTIFICATION: What is the core problem?
2. IMPACT ASSESSMENT: How does this affect the customer?
3. URGENCY SIGNALS: What indicates the priority level?
4. TECHNICAL INDICATORS: What technical details are present?
5. SENTIMENT ANALYSIS: What is the customer's emotional state?
</analysis>

<classification>
PRIMARY_CATEGORY: [select one category]
SECONDARY_CATEGORY: [if applicable]
URGENCY: [CRITICAL/HIGH/MEDIUM/LOW]
CONFIDENCE: [0.0-1.0]
ROUTING: [team assignment]
</classification>

<reasoning>
[Explain your classification decision in 2-3 sentences]
</reasoning>
```

## Variable Mappings

| Variable Name | Description | Type | Constraints | Required | Example |
|--------------|-------------|------|-------------|----------|---------|
| ticket_id | Unique ticket identifier | string | UUID format | Yes | "TCK-2024-789456" |
| customer_tier | Customer subscription level | string | Enterprise/Professional/Starter | Yes | "Enterprise" |
| product_name | Affected product/service | string | Valid product list | Yes | "Analytics Dashboard" |
| ticket_subject | Ticket subject line | string | Max 200 chars | Yes | "Dashboard not loading - URGENT" |
| ticket_body | Full ticket description | string | Max 5000 chars | Yes | "Since this morning, our entire team..." |
| attachment_count | Number of attached files | integer | 0-10 | Yes | 3 |
| recent_ticket_count | Tickets in last 30 days | integer | 0-999 | Yes | 2 |

## Token Analysis

- **Base Token Count**: 285 tokens (system + user prompt structure)
- **Average Variable Content**: 150-400 tokens
- **Estimated Max Tokens**: 750 tokens (with max variable content)
- **Token Budget Allocation**:
  - Analysis section: 200 tokens
  - Classification section: 50 tokens
  - Reasoning section: 100 tokens
  - Total output budget: 350 tokens

## Classification Schema

### Primary Categories

| Category | Description | Decision Criteria | Priority Weight |
|----------|-------------|-------------------|-----------------|
| TECHNICAL_BUG | System malfunction or error | Error messages, crashes, unexpected behavior | 0.9 |
| ACCESS_ISSUE | Login or permission problems | Authentication, authorization, account access | 0.85 |
| DATA_INTEGRITY | Data loss or corruption concerns | Missing data, incorrect calculations, sync issues | 0.95 |
| PERFORMANCE | Speed or reliability issues | Slow loading, timeouts, intermittent failures | 0.7 |
| BILLING | Payment or subscription issues | Charges, invoices, plan changes | 0.8 |
| FEATURE_REQUEST | New functionality requests | "Would like", "Can you add", enhancement language | 0.4 |
| INTEGRATION | Third-party connection issues | API, webhooks, external services | 0.75 |
| USER_EDUCATION | How-to questions | "How do I", "Where can I find", documentation needs | 0.3 |

### Urgency Levels

| Level | Description | Indicators | SLA Target |
|-------|-------------|------------|------------|
| CRITICAL | Business operations blocked | "Production down", "All users affected", data loss risk | 1 hour |
| HIGH | Significant impact | "Team blocked", "Deadline today", Enterprise customer | 4 hours |
| MEDIUM | Normal business impact | Standard issues, workarounds available | 24 hours |
| LOW | Minor inconvenience | Questions, small UI issues, feature requests | 72 hours |

### Confidence Thresholds

- **High Confidence**: 0.8-1.0 (clear indicators, unambiguous language)
- **Medium Confidence**: 0.6-0.79 (some ambiguity, multiple possible categories)
- **Low Confidence**: Below 0.6 (requires human review)

### Edge Case Rules

1. **Security Mentions**: Always escalate to CRITICAL if data breach suspected
2. **Enterprise + Production**: Auto-upgrade to HIGH minimum
3. **Multiple Categories**: Use primary for most severe, secondary for related issue
4. **Angry Tone + Technical**: Maintain technical classification but note sentiment

## Change History

### v1.2.0 (Current)
- **Changes**: Enhanced urgency detection with production-down keywords
- **Rationale**: 15% of critical issues were initially classified as HIGH
- **Impact**: Reduced critical issue response time by 23%

### v1.1.0
- **Changes**: Added sentiment analysis step
- **Rationale**: Customer satisfaction scores indicated tone wasn't considered
- **Impact**: 8% improvement in customer feedback scores

### v1.0.0
- **Initial Release**: Basic classification system
- **Coverage**: 6 primary categories
- **Accuracy**: 87% on test set