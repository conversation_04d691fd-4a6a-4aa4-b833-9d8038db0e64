# bb_core_generation - Core Generation Stage

Initial prompt creation from requirements.
Generates prompts, test cases, and requirements documentation.
bd_iteration_machine_1.py loops on prompt-only refinements.
Synthetic pipeline sub-module handles synthetic test generation.
All outputs feed into testing & evaluation stage.



in doc_gen, from everything you've read, and the     │
│   specifications provided in the repo, what are the    │
│   requirements for the PromptDoc, ScenarioDoc and      │
│   ReqDoc .md output files expected from (1) doc_gen    │
│   stage of pipeline? Once confident, list them, then   │
│   generate all three in full in parallell as e.g.,     │
│   example_ReqDoc_1.md in the dir. Spec (requirements)  │
│   just mentioned for the three docs should go in a     │
│   .md doc in the same dir as well.
