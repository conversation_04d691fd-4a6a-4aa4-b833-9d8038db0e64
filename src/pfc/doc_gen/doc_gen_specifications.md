# Document Generation Specifications

## Overview

The doc_gen stage produces three critical document types that flow through the PFC pipeline. Each document serves a specific purpose in the prompt engineering workflow.

## Document Types

### 1. PromptDoc (Prompt Documentation)

**Purpose**: Track prompt evolution through iterative refinement

**Required Sections**:
- **Metadata Header**
  - Document version (semver format)
  - Generation timestamp
  - Pipeline stage identifier
  - Parent prompt reference (if refined)

- **Prompt Template**
  - System prompt with clear role definition
  - User prompt template with {{variable}} placeholders
  - Chain-of-thought structure markers
  - Output format instructions

- **Variable Mappings**
  - Variable name → description
  - Variable type constraints
  - Example values
  - Required vs optional flags

- **Token Analysis**
  - Base token count (without variables)
  - Estimated max tokens with variables
  - Token budget allocation

- **Classification Schema**
  - Output categories with descriptions
  - Decision criteria per category
  - Priority rules for edge cases
  - Confidence thresholds

- **Change History**
  - Version-to-version diffs
  - Refinement rationale
  - Performance impact notes

### 2. ScenarioDoc (Test Scenario Documentation)

**Purpose**: Define comprehensive test cases for prompt evaluation

**Required Sections**:
- **Test Categories**
  - Category ID and name
  - Category description
  - Expected behavior definition
  - Edge case indicators

- **Test Case Templates**
  - Per-category test generation logic
  - Synthetic data templates
  - Variation parameters
  - Inspiration seeds for generation

- **Concrete Test Cases**
  - Minimum 5 clear examples per category
  - Minimum 3 edge cases per category
  - Minimum 2 borderline cases between categories
  - Expected outputs with reasoning

- **Test Chunking Strategy**
  - Chunk size recommendations
  - Category distribution requirements
  - Parallel execution groupings

- **Validation Criteria**
  - Pass/fail thresholds
  - Category-specific metrics
  - Overall accuracy targets (≥90%)

### 3. ReqDoc (Requirements Documentation)

**Purpose**: Define system requirements and success criteria

**Required Sections**:
- **System Overview**
  - Problem statement
  - Solution approach
  - Key stakeholders
  - Success metrics

- **Functional Requirements**
  - Input specifications
  - Processing requirements
  - Output specifications
  - Integration points

- **Quality Requirements**
  - Accuracy targets (overall and per-category)
  - Latency constraints
  - Token efficiency goals
  - Consistency requirements

- **Domain Specifications**
  - Technical terminology glossary
  - Business rule definitions
  - Priority hierarchies
  - Exception handling rules

- **Workflow Requirements**
  - Processing pipeline stages
  - Data flow specifications
  - Checkpoint requirements
  - Rollback conditions

- **Constraints**
  - Technical limitations
  - Business constraints
  - Regulatory requirements
  - Resource boundaries

## Document Standards

### Format Requirements
- Markdown format with clear hierarchical structure
- Tables for structured data (with divider lines per style guide)
- Code blocks for examples with language tags
- Mermaid diagrams for flows where applicable

### Naming Convention
- `{timestamp}_{docType}_{version}.md`
- Example: `2024-07-15_PromptDoc_v1.2.md`

### Cross-References
- Each document must reference related documents
- Use relative paths for internal links
- Maintain bidirectional references

### Version Control
- Semantic versioning for all documents
- Major version: Breaking changes to schema/format
- Minor version: New sections or significant content
- Patch version: Corrections and clarifications

## Integration with Pipeline

1. **Generation Phase**: doc_gen creates initial versions
2. **Evaluation Phase**: Results annotated back to ScenarioDoc
3. **Refinement Phase**: PromptDoc updated with improvements
4. **Optimization Phase**: All docs updated with final metrics

## Quality Checklist

- [ ] All required sections present
- [ ] Cross-references valid
- [ ] Examples concrete and testable
- [ ] Metrics quantifiable
- [ ] Version tracking consistent
- [ ] Pyramid principle applied to structure
- [ ] Technical accuracy verified