<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prompt Generator - Real-time LLM Conversation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Stepper at the top -->
            <div class="container-fluid mt-3 sticky-stepper">
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-center align-items-center">
                            <div class="stepper-step" id="step1">
                                <span class="stepper-circle">1</span>
                                <span class="stepper-label">Generate Prompt</span>
                            </div>
                            <div class="stepper-line"></div>
                            <div class="stepper-step" id="step2">
                                <span class="stepper-circle">2</span>
                                <span class="stepper-label">Generate Test Data</span>
                            </div>
                            <div class="stepper-line"></div>
                            <div class="stepper-step" id="step3">
                                <span class="stepper-circle">3</span>
                                <span class="stepper-label">Run Prompt on Test Data</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Left Panel - Controls -->
            <div class="col-md-4">
                <div class="sticky-panel">
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5>Prompt Generator</h5>
                        </div>
                        <div class="card-body">
                            <form id="promptForm">
                                <div class="mb-3">
                                    <label for="task" class="form-label">Task Description</label>
                                    <textarea class="form-control" id="task" rows="4" placeholder="Describe what you want the prompt to do...">Create a simple greeting bot</textarea>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="targetScore" class="form-label">Target Score (0-10)</label>
                                            <input type="number" class="form-control" id="targetScore" value="8.5" min="0" max="10" step="0.1">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="maxTurns" class="form-label">Max Turns</label>
                                            <input type="number" class="form-control" id="maxTurns" value="10" min="1" max="50">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="testCaseCount" class="form-label">Number of Test Cases</label>
                                    <input type="number" class="form-control" id="testCaseCount" value="5" min="1" max="10">
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100" id="generateBtn">
                                    <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                                    Generate Prompt
                                </button>
                                <button type="button" class="btn btn-success w-100 mt-2" id="generateTestDataBtn" disabled>Generate Test Input Data</button>
                                <button type="button" class="btn btn-info w-100 mt-2" id="runPromptOnTestDataBtn" disabled>Run Prompt on Test Data</button>
                            </form>
                            
                            <div class="mt-3">
                                <button class="btn btn-outline-secondary btn-sm" onclick="clearLogs()">Clear Logs</button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="downloadPrompt()">Download Prompt</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Status Panel -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6>Status</h6>
                        </div>
                        <div class="card-body">
                            <div id="statusDisplay">
                                <span class="badge bg-secondary">Ready</span>
                            </div>
                            <div id="progressInfo" class="mt-2 d-none">
                                <small class="text-muted">
                                    Turn: <span id="currentTurn">-</span> | 
                                    Role: <span id="currentRole">-</span>
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Add a reset button at the bottom of the left panel -->
                    <div class="mt-4 text-center">
                        <button class="btn btn-outline-danger btn-sm" id="resetWorkflowBtn">Reset Workflow</button>
                    </div>
                </div>
            </div>
            
            <!-- Right Panel - Real-time Output -->
            <div class="col-md-8">
                <div class="card mt-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5>Real-time Conversation</h5>
                        <div>
                            <span class="badge bg-info" id="messageCount">0 messages</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="conversationLog" class="conversation-log">
                            <div class="text-muted text-center py-4">
                                Enter a task and click "Generate Prompt" to start...
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Final Prompt Display -->
                <div class="card mt-3 d-none" id="finalPromptCard">
                    <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">✅ Final Generated Prompt</h5>
                        <button class="btn btn-light btn-sm" onclick="copyFinalPrompt()">Copy</button>
                    </div>
                    <div class="card-body" style="background:#f6fff6;">
                        <textarea id="finalPrompt" class="form-control" rows="10" style="font-family: 'Fira Mono', 'Consolas', monospace; font-size:1rem; line-height:1.5; min-height:180px; background:#f8f9fa; border:1px solid #b2dfb2; border-radius:0.25rem; resize:vertical;"></textarea>
                    </div>
                </div>

                <!-- Section to display generated test input data -->
                <div class="card mt-3 d-none" id="testDataCard">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">Generated Test Input Data</h6>
                    </div>
                    <div class="card-body">
                        <pre id="testDataDisplay" style="font-size:0.95rem;"></pre>
                    </div>
                </div>

                <!-- Section to display prompt execution results -->
                <div class="card mt-3 d-none" id="testResultsCard">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="mb-0">Prompt Execution Results</h6>
                    </div>
                    <div class="card-body">
                        <div id="testResultsTable"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
    <script>
    function copyFinalPrompt() {
        const el = document.getElementById('finalPrompt');
        if (!el.textContent) return;
        navigator.clipboard.writeText(el.textContent).then(() => {
            alert('Prompt copied to clipboard!');
        });
    }
    </script>
</body>
</html> 