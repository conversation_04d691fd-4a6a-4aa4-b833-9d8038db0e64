import concurrent.futures
import json
import os
import queue
import re
import sys
import threading
import time

from flask import Flask, jsonify, render_template, request
from flask_socketio import SocketIO, emit

# Add OpenAI import for LLM calls
from openai import OpenAI
from prompt_generator.config import load_config
from prompt_generator.orchestrator import Orchestrator

app = Flask(__name__)
app.config["SECRET_KEY"] = "your-secret-key-here"
socketio = SocketIO(app, cors_allowed_origins="*")

# Global queue for communication between threads
message_queue = queue.Queue()

# Helper: get OpenAI client
config = load_config()
api_key = os.environ.get("OPENAI_API_KEY") or config.get("openai_api_key")
openai_client = OpenAI(api_key=api_key)

class WebSocketHandler:
    """Custom handler to capture output and send to WebSocket"""

    def __init__(self, socketio, room):
        self.socketio = socketio
        self.room = room
        self.buffer = ""

    def write(self, text):
        self.buffer += text
        if "\n" in text:
            lines = self.buffer.split("\n")
            self.buffer = lines[-1]  # Keep incomplete line in buffer
            for line in lines[:-1]:
                if line.strip():
                    self.socketio.emit("log_message", {
                        "message": line.strip(),
                        "timestamp": time.strftime("%H:%M:%S")
                    }, room=self.room)

    def flush(self):
        if self.buffer.strip():
            self.socketio.emit("log_message", {
                "message": self.buffer.strip(),
                "timestamp": time.strftime("%H:%M:%S")
            }, room=self.room)
            self.buffer = ""

def run_orchestrator_with_websocket(task, target_score, max_turns, room):
    """Run the orchestrator and send updates via WebSocket"""
    try:
        # Capture stdout to send to WebSocket
        old_stdout = sys.stdout
        web_handler = WebSocketHandler(socketio, room)
        sys.stdout = web_handler

        # Send initial status
        socketio.emit("status_update", {
            "status": "starting",
            "message": f'Starting prompt generation for: "{task}"'
        }, room=room)

        # Initialize orchestrator
        orchestrator = Orchestrator(target_score=target_score, max_turns=max_turns)

        # Initial state
        state = {
            "task": task,
            "target_score": target_score,
        }

        socketio.emit("status_update", {
            "status": "running",
            "message": "Orchestrator initialized, beginning prompt generation..."
        }, room=room)

        # Run the orchestration
        for turn in range(1, max_turns + 1):
            role_name, reason = orchestrator._policy(state)
            if role_name is None:
                state["termination_reason"] = reason
                socketio.emit("status_update", {
                    "status": "completed",
                    "message": f"Termination: {reason}"
                }, room=room)
                break

            role = orchestrator.roles.get(role_name)
            if role is None:
                raise KeyError(f"Role '{role_name}' not found")

            # Send turn update
            socketio.emit("turn_update", {
                "turn": turn,
                "role": role_name,
                "reason": reason
            }, room=room)

            # Capture the draft before running the role
            previous_draft = state.get("draft", "")

            # Run the role
            output = role.eval(state)
            state.update(output.to_state_update())
            state["_last_role"] = role_name

            # Send draft update if it changed
            current_draft = state.get("draft", "")
            if current_draft != previous_draft:
                socketio.emit("draft_update", {
                    "draft": current_draft,
                    "role": role_name,
                    "turn": turn
                }, room=room)

            # Send critic feedback if available
            if "critic_feedback" in state and role_name == "Critic":
                socketio.emit("critic_feedback", {
                    "score": state.get("critic_score", "N/A"),
                    "feedback": state["critic_feedback"],
                    "turn": turn
                }, room=room)

            # Small delay to make the UI more readable
            time.sleep(0.5)

        # Send final result
        final_prompt = state.get("draft", "<no prompt generated>")
        socketio.emit("final_result", {
            "prompt": final_prompt,
            "termination_reason": state.get("termination_reason", "Unknown"),
            "total_turns": turn - 1 if "turn" in locals() else 0
        }, room=room)

    except Exception as e:
        socketio.emit("error", {
            "message": f"Error: {e!s}"
        }, room=room)
    finally:
        # Restore stdout
        sys.stdout = old_stdout

@app.route("/")
def index():
    return render_template("index.html")

@app.route("/api/generate", methods=["POST"])
def generate_prompt():
    data = request.json
    task = data.get("task", "")
    target_score = float(data.get("target_score", 8.5))
    max_turns = int(data.get("max_turns", 10))

    if not task:
        return jsonify({"error": "Task description is required"}), 400

    # Generate a unique room ID for this session
    room = f"session_{int(time.time())}"

    # Start the orchestration in a separate thread
    thread = threading.Thread(
        target=run_orchestrator_with_websocket,
        args=(task, target_score, max_turns, room)
    )
    thread.daemon = True
    thread.start()

    return jsonify({
        "room": room,
        "message": "Generation started"
    })

@socketio.on("join")
def on_join(data):
    room = data["room"]
    from flask_socketio import join_room
    join_room(room)
    emit("status", {"msg": f"Joined room: {room}"})

@socketio.on("disconnect")
def on_disconnect():
    print("Client disconnected")

def call_llm_for_test_data(prompt):
    """Call LLM to generate context-aware test data as a JSON object."""
    system_prompt = (
        "You are a test data generator for prompt engineering.\n"
        "Given the following prompt:\n" + prompt + "\n\n"
        "Decide what types of input data (emails, business documents, web search results, website snippets, etc.) "
        "would be most appropriate to test this prompt.\n"
        "Generate a realistic set of test inputs (at least 3, up to 10), using the types you think are best.\n"
        "Output your test data as a JSON object, with keys for each type (e.g., 'emails', 'documents', 'web_search_results', etc.)."
    )
    response = openai_client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": "Generate the test data now."}
        ],
        temperature=0.7,
        max_tokens=800
    )
    # Extract JSON from response
    content = response.choices[0].message.content
    # Try to extract JSON block if present
    match = re.search(r"\{[\s\S]*\}", content)
    if match:
        json_str = match.group(0)
        try:
            return json.loads(json_str)
        except Exception:
            try:
                return ast.literal_eval(json_str)
            except Exception:
                return {"raw": content}
    else:
        return {"raw": content}

# Helper: flatten test data for prompt execution

def flatten_test_data(test_data):
    """Yield (type, input_str) for each test input in the test_data dict."""
    for key, items in test_data.items():
        if isinstance(items, list):
            for item in items:
                if isinstance(item, dict):
                    # For web_search_results, website_snippets, etc.
                    input_str = json.dumps(item, indent=2)
                else:
                    input_str = str(item)
                yield (key, input_str)

def extract_system_user_with_llm(prompt):
    """Use LLM to extract or transform prompt into system/user messages."""
    extraction_prompt = (
        "You are a prompt parser.\n"
        "Given the following prompt, extract the system message and the user message as they would be sent to an LLM API (e.g., OpenAI Chat API).\n"
        "Output a JSON object with two fields: system and user. If you can't find one, leave it as an empty string.\n"
        "PROMPT:\n" + prompt
    )
    response = openai_client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {"role": "system", "content": extraction_prompt}
        ],
        temperature=0.0,
        max_tokens=600
    )
    import json
    import re
    content = response.choices[0].message.content
    # Try to extract JSON block if present
    match = re.search(r"\{[\s\S]*\}", content)
    if match:
        json_str = match.group(0)
        try:
            result = json.loads(json_str)
            return result.get("system", "").strip(), result.get("user", "").strip()
        except Exception:
            pass
    # Fallback: treat whole prompt as system message
    return prompt, ""

# Cache for extraction results (per prompt)
_extraction_cache = {}

def call_llm_with_prompt_and_input(prompt, input_str):
    # Use LLM to extract system/user messages
    if prompt in _extraction_cache:
        system_msg, user_msg = _extraction_cache[prompt]
    else:
        system_msg, user_msg = extract_system_user_with_llm(prompt)
        _extraction_cache[prompt] = (system_msg, user_msg)
    if system_msg and user_msg:
        messages = [
            {"role": "system", "content": system_msg},
            {"role": "user", "content": user_msg + "\n" + input_str}
        ]
    else:
        messages = [
            {"role": "system", "content": prompt},
            {"role": "user", "content": input_str}
        ]
    response = openai_client.chat.completions.create(
        model="gpt-4o",
        messages=messages,
        temperature=0.7,
        max_tokens=800
    )
    return response.choices[0].message.content.strip()

def decide_test_case_types(prompt, test_case_count):
    """Call LLM to decide what types of test cases to generate and how many of each."""
    system_prompt = (
        "You are a test data planner for prompt engineering.\n"
        "Given the following prompt:\n" + prompt + "\n\n"
        f"The user wants to generate {test_case_count} test cases.\n"
        "Decide what types of input data (emails, business documents, web search results, website snippets, etc.) "
        "would be most appropriate to test this prompt.\n"
        "Distribute the test cases among the types you choose.\n"
        "Output a JSON object with keys as types and values as the number of test cases for each type."
    )
    response = openai_client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": "Plan the test case types now."}
        ],
        temperature=0.3,
        max_tokens=300
    )
    content = response.choices[0].message.content
    match = re.search(r"\{[\s\S]*\}", content)
    if match:
        json_str = match.group(0)
        try:
            return json.loads(json_str)
        except Exception:
            try:
                return ast.literal_eval(json_str)
            except Exception:
                return {"raw": content}
    else:
        return {"raw": content}

def clean_test_case(text):
    import re
    # Remove code fences and leading/trailing whitespace
    text = re.sub(r"^```[a-zA-Z]*\n?", "", text)
    text = re.sub(r"```$", "", text)
    text = text.strip()
    # Filter out refusal messages
    if text.lower().startswith("i'm sorry") or "can't assist" in text.lower() or not text:
        return None
    return text

def generate_single_test_case(prompt, typ, max_retries=2):
    """Call LLM to generate a single test case of the given type, with cleaning, retry, and more detailed, filled-out input."""
    user_prompt = (
        f"Given the following system prompt: {prompt}\n"
        f"Generate a realistic {typ.replace('_', ' ')} as test input for this prompt.\n"
        f"Come up with plausible, detailed values for all variables or placeholders (e.g., [[COMPANY_NAME]], [[REGION_OR_SEGMENT]], etc.) as if you were a real user or a real-world scenario.\n"
        f"Make the input substantial and realistic—at least 10-15 sentences or 2-3 full paragraphs, not just a short phrase.\n"
        f"Include rich context, specific details, realistic scenarios, and comprehensive information that would challenge the prompt's capabilities.\n"
        f"Do not wrap your response in code fences or markdown.\n"
        f"Do not apologize or refuse; always generate a realistic input.\n"
        f"Only return the raw input data, nothing else."
    )
    for attempt in range(max_retries+1):
        response = openai_client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": user_prompt}
            ],
            temperature=0.8,
            max_tokens=1500
        )
        content = response.choices[0].message.content.strip()
        cleaned = clean_test_case(content)
        # Require at least 10 sentences or 2 full paragraphs (min 1000 chars)
        if cleaned and (cleaned.count(".") + cleaned.count("!") + cleaned.count("?") >= 10 or len(cleaned) >= 1000):
            return typ, cleaned
    # If all retries fail, return the last attempt (even if refusal/short)
    return typ, cleaned if cleaned else content

def generate_test_data_parallel(prompt, test_case_count):
    plan = decide_test_case_types(prompt, test_case_count)
    if not isinstance(plan, dict):
        return {"error": "Could not determine test case types.", "raw": plan}
    results = {}
    with concurrent.futures.ThreadPoolExecutor() as executor:
        futures = []
        for typ, count in plan.items():
            try:
                n = int(count)
            except Exception:
                n = 1
            for _ in range(n):
                futures.append(executor.submit(generate_single_test_case, prompt, typ))
        for future in concurrent.futures.as_completed(futures):
            typ, test_case = future.result()
            if test_case:
                results.setdefault(typ, []).append(test_case)
    return results

@app.route("/api/generate_test_data", methods=["POST"])
def api_generate_test_data():
    data = request.json
    prompt = data.get("prompt", "")
    test_case_count = int(data.get("test_case_count", 5))
    if not prompt:
        return jsonify({"error": "Prompt is required"}), 400
    test_data = generate_test_data_parallel(prompt, test_case_count)
    return jsonify(test_data)

# API endpoint: run prompt on test data
@app.route("/api/run_prompt_on_test_data", methods=["POST"])
def api_run_prompt_on_test_data():
    data = request.json
    prompt = data.get("prompt", "")
    test_data = data.get("test_data", {})
    if not prompt or not test_data:
        return jsonify({"error": "Prompt and test_data are required"}), 400
    results = []
    for typ, input_str in flatten_test_data(test_data):
        output = call_llm_with_prompt_and_input(prompt, input_str)
        results.append({"input": f"[{typ}]\n{input_str}", "output": output})
    return jsonify(results)

if __name__ == "__main__":
    socketio.run(app, debug=True, host="0.0.0.0", port=5001)
