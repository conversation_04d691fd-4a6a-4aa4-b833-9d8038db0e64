.conversation-log {
    height: 500px;
    overflow-y: auto;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.message {
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    border-radius: 0.25rem;
    border-left: 4px solid #dee2e6;
}

.message.log {
    background-color: #ffffff;
    border-left-color: #6c757d;
}

.message.turn {
    background-color: #e3f2fd;
    border-left-color: #2196f3;
    font-weight: bold;
}

.message.draft {
    background-color: #f3e5f5;
    border-left-color: #9c27b0;
}

.message.critic {
    background-color: #fff3e0;
    border-left-color: #ff9800;
}

.message.status {
    background-color: #e8f5e8;
    border-left-color: #4caf50;
}

.message.error {
    background-color: #ffebee;
    border-left-color: #f44336;
}

.timestamp {
    color: #6c757d;
    font-size: 0.8rem;
    margin-right: 0.5rem;
}

.role-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: bold;
    border-radius: 0.25rem;
    margin-right: 0.5rem;
}

.role-writer { background-color: #e3f2fd; color: #1976d2; }
.role-critic { background-color: #fff3e0; color: #f57c00; }
.role-editor { background-color: #f3e5f5; color: #7b1fa2; }
.role-outputguardian { background-color: #e8f5e8; color: #388e3c; }
.role-tokenoptimizer { background-color: #fff8e1; color: #fbc02d; }

.draft-content {
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 1rem;
    margin-top: 0.5rem;
    white-space: pre-wrap;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    max-height: 300px;
    overflow-y: auto;
}

.critic-score {
    font-size: 1.2rem;
    font-weight: bold;
    color: #ff9800;
}

.progress-bar {
    height: 4px;
    background-color: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
    margin-top: 0.5rem;
}

.progress-fill {
    height: 100%;
    background-color: #007bff;
    transition: width 0.3s ease;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

#finalPromptCard {
    border-left: 4px solid #28a745;
}

#finalPromptCard .card-header {
    background: #28a745 !important;
    color: #fff !important;
    border-bottom: 1px solid #218838;
}

#finalPromptCard .btn {
    border: none;
    background: #e9fbe5;
    color: #218838;
    font-weight: bold;
    transition: background 0.2s;
}

#finalPromptCard .btn:hover {
    background: #c3f7c0;
    color: #155724;
}

#finalPromptCard .card-body {
    background: #f6fff6 !important;
    border-radius: 0 0 0.25rem 0.25rem;
    padding: 1.5rem 1rem 1rem 1rem;
}

#finalPrompt {
    background: #f8f9fa;
    border: 1px solid #b2dfb2;
    border-radius: 0.25rem;
    padding: 1.25rem;
    margin: 0;
    white-space: pre-wrap;
    font-size: 1rem;
    font-family: 'Fira Mono', 'Consolas', monospace;
    line-height: 1.5;
    color: #222;
    max-height: 400px;
    overflow-y: auto;
    box-shadow: 0 0.125rem 0.25rem rgba(40, 167, 69, 0.05);
}

.typing-indicator {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.scroll-to-bottom {
    scroll-behavior: smooth;
}

/* Stepper styles */
.stepper-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120px;
}
.stepper-circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #e9ecef;
  color: #495057;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.2rem;
  margin-bottom: 4px;
  border: 2px solid #dee2e6;
  transition: background 0.2s, color 0.2s, border 0.2s;
}
.stepper-step.active .stepper-circle {
  background: #007bff;
  color: #fff;
  border-color: #007bff;
}
.stepper-step.completed .stepper-circle {
  background: #28a745;
  color: #fff;
  border-color: #28a745;
}
.stepper-label {
  font-size: 0.95rem;
  color: #495057;
}
.stepper-line {
  flex: 1;
  height: 2px;
  background: #dee2e6;
  margin: 0 8px;
}
.stepper-step.completed .stepper-label {
  color: #28a745;
}
.stepper-step.active .stepper-label {
  color: #007bff;
}

/* Section spacing */
.card.mt-3 {
  margin-top: 2rem !important;
}

/* Spinner styles */
.spinner-border-sm, .spinner-border {
  vertical-align: middle;
}

/* Hide sections by default */
#testDataCard, #testResultsCard, #finalPromptCard {
  transition: opacity 0.2s;
}

/* Results table improvements */
#testResultsTable table {
  font-size: 0.97rem;
}
#testResultsTable pre {
  background: #f8f9fa;
  border-radius: 0.25rem;
  padding: 0.5rem;
  margin: 0;
  font-size: 0.97rem;
  font-family: 'Fira Mono', 'Consolas', monospace;
  white-space: pre-wrap;
}
#testResultsTable td {
  vertical-align: top;
}

/* Reset button */
#resetWorkflowBtn {
  margin-top: 1.5rem;
}

/* Sticky left panel */
.sticky-panel {
  position: sticky;
  top: 90px;
  z-index: 10;
}

/* Sticky stepper/progress bar */
.sticky-stepper {
  position: sticky;
  top: 0;
  z-index: 100;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

@media (max-width: 991px) {
  .sticky-panel {
    position: static;
    top: auto;
  }
  .sticky-stepper {
    position: static;
    top: auto;
    box-shadow: none;
  }
} 