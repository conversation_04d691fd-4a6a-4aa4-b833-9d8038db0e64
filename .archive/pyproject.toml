[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "prompt_generator"
version = "0.1.0"
authors = [
    { name = "Your Name", email = "<EMAIL>" },
]
description = "A custom prompt generator for hedge fund analysis"
requires-python = ">=3.8"
dependencies = [
    "openai>=1.0.0",
    "pydantic>=2.0.0",
    "rich>=10.0.0",
    "typer>=0.9.0",
]

[project.scripts]
prompt-generator = "prompt_generator.cli:main"

[tool.setuptools.packages.find]
include = ["prompt_generator*"]
exclude = ["static*", "templates*", "venv*", "*.egg-info*"] 