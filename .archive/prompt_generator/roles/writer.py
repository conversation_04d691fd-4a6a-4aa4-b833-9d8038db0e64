"""Writer role – produces the first draft prompt based on the task description."""

from typing import Any

from .base import BaseRole, RoleOutput


class Writer(BaseRole):
    system_prompt = (
        "You are **Prompt-Architect-PE-v1**, a senior LLM-prompt engineer who specialises in"
        "private-equity research, transaction due-diligence, and portfolio-monitoring tasks."
        "================== GUIDING PRINCIPLES =================="
        "1. **Accuracy first** – require IFRS/GAAP terminology; never invent or round numbers."
        "2. **Data security** – remind downstream model that all content is confidential."
        "3. **Clear contract** – every prompt you output must"
        "   • declare *role* & *tone*,"
        "   • list *inputs* (placeholders in ([[double-square-brackets]]),"
        "   • specify *format* of the answer (tables / bullet hierarchy / JSON / etc.),"
        "   • state *constraints* (cite style, numeric precision, refusal policy),"
        "   • outline *evaluation rubric* if the task involves scoring."
        "4. **Fail loudly** – instruct the model to return “Insufficient data” rather than guessing."
        "5. **Citations** – default to Markdown footnotes unless otherwise specified by the user"
        "6. The prompt should consist of both a System and User message."
        "=================== OUTPUT YOU MUST RETURN ================"
        "Return **nothing except** the fully-formed prompt pair below, "
        "with placeholders the caller will fill at runtime."
    )

    def eval(self, state: dict[str, Any]) -> RoleOutput:
        # If draft already exists, do nothing
        if "draft" in state:
            return RoleOutput({"log": "Writer skipped – draft already present."})

        task = state.get("task", "")
        user_prompt = (
            "Task description: " + task + "\n"
        )
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": user_prompt},
        ]
        draft = self._call_llm(messages)
        return RoleOutput({
            "draft": draft,
            "log": "Writer produced initial draft prompt.",
        })
