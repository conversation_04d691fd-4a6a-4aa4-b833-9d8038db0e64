from __future__ import annotations

import abc
from dataclasses import dataclass, field
from typing import Any

# OpenAI v1.0+ uses different import paths
from openai import APIConnectionError, APIError, AuthenticationError, BadRequestError, OpenAI, RateLimitError
from pydantic import BaseModel

# Local import guarded to avoid circularity – resolved at runtime
from ..config import settings


@dataclass
class RoleOutput:
    """Standardised output from a role evaluation.

    Each role may add arbitrary keys to update shared *state* but MUST include
    a `log` key with human-readable notes explaining what it did. That ensures
    the orchestrator can always display something meaningful.
    """

    data: dict[str, Any] = field(default_factory=dict)

    def to_state_update(self) -> dict[str, Any]:
        """Return key/value pairs that will be merged into orchestration state."""
        return self.data


class BaseRole(abc.ABC):
    """Abstract base class every role must subclass."""

    name: str

    def __init__(self, name: str | None = None):
        self.name = name or self.__class__.__name__

    # System prompt inserted before user messages for LLM-based roles
    system_prompt: str | None = None

    # OpenAI model identifier. Subclasses can override via __init__ later
    model: str = "o3-2025-04-16" # Use the o3 model as preferred

    # Temperature for generation (where applicable)
    temperature: float = 1.0

    # Max tokens for generation - will be overridden by config
    max_tokens: int = 10000

    @abc.abstractmethod
    def eval(self, state: dict[str, Any]) -> RoleOutput:
        """Run the role on current *state* and return updates.

        Subclasses should NOT mutate *state* in-place; instead return a
        RoleOutput whose ``data`` will be merged by the orchestrator.
        """

    # Helper -----------------------------------------------------------------
    def _call_llm(self, messages: list[dict[str, str]], **kwargs) -> str:
        """Utility wrapper to send ChatCompletion request.

        Falls back to providing a mock response if API is unavailable.
        """
        try:
            # Check if API key exists and is valid format
            if not settings.openai_api_key or len(settings.openai_api_key) < 20:
                print("Warning: Invalid or missing OpenAI API key. Using mock response.")
                return self._mock_response(messages)

            client = OpenAI(api_key=settings.openai_api_key)

            # Keep the o3 model as is (don't replace with gpt-4)
            model_to_use = kwargs.get("model", self.model)

            # Set a reasonable token limit to prevent context length errors
            max_tokens = min(kwargs.get("max_tokens", 5000), settings.max_completion_tokens)

            print(f"Requesting completion with model {model_to_use}, max_tokens: {max_tokens}")

            response = client.chat.completions.create(
                model=model_to_use,
                messages=messages,
                temperature=kwargs.get("temperature", self.temperature),
                max_completion_tokens=max_tokens,  # Changed from max_tokens to max_completion_tokens
            )
            return response.choices[0].message.content.strip()

        except (AuthenticationError, BadRequestError) as e:
            print(f"API Error: {e!s}")
            return self._mock_response(messages)
        except (APIError, APIConnectionError, RateLimitError) as e:
            print(f"API Error: {e!s}")
            return self._mock_response(messages)
        except Exception as e:
            print(f"Unexpected error: {e!s}")
            return self._mock_response(messages)

    def _mock_response(self, messages: list[dict[str, str]]) -> str:
        """Generate a mock response for demonstration purposes."""
        if self.name == "Writer":
            return """System Message:
You are WeatherBot, a helpful assistant that provides weather forecasts.
You must always respond with accurate and up-to-date information.
You should never make up weather data, but instead ask for clarification.

User Message:
I need to know the weather forecast for {{LOCATION}} on {{DATE}}.
Please provide temperature, precipitation chance, and wind conditions."""
        if self.name == "Critic":
            return "The prompt is good but could be improved. Score: 7.5/10"
        if self.name == "Editor":
            return """System Message:
You are "WeatherBot", a precise and reliable assistant that provides weather forecasts.
You must always respond with accurate and up-to-date information based on reliable weather data sources.
You should never make up weather data. If data is unavailable or location is unclear, ask for clarification.
All user data should be treated as confidential and not shared or stored.

User Message:
I need to know the weather forecast for {{LOCATION}} on {{DATE}}.
Please provide:
- Temperature (high/low in °C and °F)
- Precipitation chance (%)
- Wind conditions (speed in km/h and mph, plus direction)
- Air quality index (with interpretation)
- Any weather warnings or alerts

If the location is ambiguous, ask for more specific information before providing a forecast."""
        if self.name == "OutputGuardian":
            return "Format is compliant."
        if self.name == "TokenOptimizer":
            # Return the same text as provided (no optimization in mock mode)
            for msg in messages:
                if msg["role"] == "user" and "draft" in msg["content"]:
                    return msg["content"].split("draft:", 1)[1].strip()
            return "Token optimization complete."
        return "Mock response for " + self.name
    # ------------------------------------------------------------------------

class Role(BaseModel):
    name: str
    description: str
    model: str = "o4-mini-2025-04-16"  # Use the o3 model as preferred
    temperature: float = 0.7
    max_tokens: int = 5000  # Default to a more reasonable limit
    system_prompt: str = ""

    def __init__(self, **data):
        super().__init__(**data)
        self.client = OpenAI(api_key=settings.openai_api_key)

    def _call_llm(self, messages: list[dict[str, str]], **kwargs) -> str:
        """Call the LLM with the given messages."""
        try:
            # Check if API key exists and is valid format
            if not settings.openai_api_key or len(settings.openai_api_key) < 20:
                print("Warning: Invalid or missing OpenAI API key. Using mock response.")
                return self._mock_response(messages)

            # Keep the o3 model
            model_to_use = kwargs.get("model", self.model)

            # Set a reasonable token limit to prevent context length errors
            max_tokens = min(kwargs.get("max_tokens", 3000), settings.max_completion_tokens)

            response = self.client.chat.completions.create(
                model=model_to_use,
                messages=messages,
                temperature=kwargs.get("temperature", self.temperature),
                max_completion_tokens=max_tokens,  # Changed from max_tokens to max_completion_tokens
            )
            return response.choices[0].message.content

        except (AuthenticationError, BadRequestError) as e:
            print(f"API Error: {e!s}")
            return self._mock_response(messages)
        except (APIError, APIConnectionError, RateLimitError) as e:
            print(f"API Error: {e!s}")
            return self._mock_response(messages)
        except Exception as e:
            print(f"Unexpected error: {e!s}")
            return self._mock_response(messages)

    def _mock_response(self, messages: list[dict[str, str]]) -> str:
        """Generate a mock response for demonstration purposes."""
        return f"Mock response for {self.name}"

    def eval(self, state: dict[str, Any]) -> dict[str, Any]:
        """Evaluate the current state and return the next state."""
        raise NotImplementedError("Subclasses must implement eval()")
