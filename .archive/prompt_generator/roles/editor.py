"""Editor role – revises the draft based on Critic feedback."""

import json
from typing import Any

from .base import BaseRole, RoleOutput


class Editor(BaseRole):
    system_prompt = (
        "You are **Prompt-Reviser-PE-v1**, a senior prompt editor.\n"

        "=============  TASK  =============\n"
        "You will receive:\n"
        "1. Draft – the current prompt pair (system + user).\n"
        "2. Feedback – a JSON object with fields:\n"
         "   • score (float 0-10)\n"
         "   • subscores      // clarity, structure, domain_safety, completeness, actionability\n"
         "   • strengths[]    // bullet strings\n"
         "   • weaknesses[]   // bullet strings (concrete fixes)\n"
         "   • suggestions[]  // bullet strings (concrete fixes)\n"

        "Revise the draft so that **every weakness is fixed and every suggestion is applied**, while preserving its strengths and overall intent.\n"

        "=============  OUTPUT  =============\n"
        "Return **only** the fully-updated prompt pair, with the same wrappers used\n"
        "in the draft (e.g., `<s>…</s><user>…</user>`).  \n"
        "Do **NOT** add commentary, JSON, or markdown.\n"

        "=============  RULES  =============\n"
        "• Retain role names and placeholders exactly as they appear unless the suggestion says to rename them.  \n"
        "• Do not invent data – if the draft lacks Info X and no suggestion provides it, leave a clear placeholder.  \n"
        '• Ensure PE guard-rails remain intact: confidentiality, GAAP accuracy, "Insufficient data" rule.  \n'
        "• Keep citation style default to Markdown footnotes unless otherwise specified by the user"
    )

    output_format_prompt = (
        "You are **Prompt-Format-Fixer**, a specialized prompt editor.\n"

        "=============  TASK  =============\n"
        "You have been given a draft prompt that is missing required format markers.\n"
        "Your job is to modify the prompt to include the appropriate format marker.\n"

        "=============  REQUIRED FORMAT  =============\n"
        "The prompt MUST include one of the following markers:\n"
        "1. JSON_OUTPUT: - Use when the prompt requires structured JSON output\n"
        "2. MARKDOWN_OUTPUT: - Use when the prompt requires formatted markdown output\n"

        "Add the appropriate marker based on the content and requirements in the prompt.\n"
        "Place the marker at the beginning of the 'Output Format' section.\n"

        "=============  OUTPUT  =============\n"
        "Return the complete revised prompt with the format marker included.\n"
        "Do not add any additional commentary.\n"
    )

    def eval(self, state: dict[str, Any]) -> RoleOutput:
        draft = state.get("draft")

        # Check if we're fixing format issues from OutputGuardian
        if draft and not state.get("output_guardian_pass", False) and state.get("output_guardian_feedback"):
            return self._fix_output_format(draft, state["output_guardian_feedback"])

        # Otherwise, handle normal editing based on Critic feedback
        critique = state.get("critic_feedback")
        if not draft or not critique:
            return RoleOutput({"log": "Editor skipped – missing draft or feedback."})

        # Convert critique to JSON string if it's a dict
        feedback_json = json.dumps(critique, indent=2) if isinstance(critique, dict) else str(critique)

        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": (
                "<<<DRAFT>>>\n" + draft + "\n<<<END>>>\n\n" +
                "<<<FEEDBACK_JSON>>>\n" + str(critique) + "\n<<<END>>>\n\n" +
                "Revise the draft so every weakness is fixed and every suggestion applied, " +
                "while preserving its strengths.\n\n" +
                "Return only the updated prompt pair with the same wrapper tags—no extra text."
            )},
        ]
        new_draft = self._call_llm(messages)
        return RoleOutput({
            "draft": new_draft,
            "log": "Editor produced revised draft.",
        })

    def _fix_output_format(self, draft: str, guardian_feedback: str) -> RoleOutput:
        """Fix format issues reported by OutputGuardian."""
        messages = [
            {"role": "system", "content": self.output_format_prompt},
            {"role": "user", "content": (
                f"The OutputGuardian reported: {guardian_feedback}\n\n"
                f"Here is the draft that needs to have a format marker added:\n\n"
                f"<<<DRAFT>>>\n{draft}\n<<<END>>>\n\n"
                f"Please add either JSON_OUTPUT: or MARKDOWN_OUTPUT: based on what's most appropriate for this prompt."
            )},
        ]
        fixed_draft = self._call_llm(messages)
        return RoleOutput({
            "draft": fixed_draft,
            "log": "Editor added required output format marker.",
            "format_fixed": True  # Flag to indicate we fixed format issues
        })
