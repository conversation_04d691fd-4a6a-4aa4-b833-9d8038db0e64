"""Orchestrator – decides which role should act next based on current state."""

from __future__ import annotations

from typing import Any

from .config import load_config
from .roles import discover_roles

# Load defaults from config.yaml
config = load_config()
DEFAULT_TARGET_SCORE = config.get("target_score", 8.0)
DEFAULT_MAX_TURNS = config.get("max_turns", 10)


class Orchestrator:
    """Manages conversational turns between virtual teammates."""

    def __init__(self, target_score: float = DEFAULT_TARGET_SCORE, max_turns: int = DEFAULT_MAX_TURNS):
        self.target_score = target_score
        self.max_turns = max_turns
        self.roles = discover_roles()

    # ---------------------------------------------------------------------
    # Policy – can be replaced by LLM-driven policy later
    # ---------------------------------------------------------------------
    def _policy(self, state: dict[str, Any]) -> tuple[str | None, str]:
        """Deterministic policy function.

        Returns (next_role_name | None, reason)
        """
        # 1. Need initial draft
        if "draft" not in state:
            return "Writer", "Need initial draft"

        # 2. Ensure output format compliance first
        if "output_guardian_pass" in state:
            # If OutputGuardian ran and failed, and we haven't fixed format yet
            if not state["output_guardian_pass"] and state.get("_last_role") == "OutputGuardian":
                # Don't try editor if we already did and it didn't help
                if state.get("format_fixed"):
                    # We already tried to fix format, but it didn't work
                    # Skip to Critic to at least get some feedback
                    return "Critic", "Proceed with quality evaluation despite format issues"
                return "Editor", "Fix output format based on guardian feedback"

        # If OutputGuardian hasn't run or we need to check again after Editor
        if not state.get("output_guardian_pass", False) and state.get("_last_role") != "OutputGuardian":
            return "OutputGuardian", "Check output format compliance"

        # 3. Evaluate quality if we haven't yet
        if "critic_score" not in state:
            return "Critic", "First quality assessment"

        # 4. If score below target, alternate between Editor and Critic
        if state.get("critic_score", 0) < self.target_score:
            # Check if the last role was Editor - if so, we should re-evaluate with Critic
            if state.get("_last_role") == "Editor":
                return "Critic", "Re-evaluate after editing"
            # Otherwise, use Editor to improve the draft
            return "Editor", "Improve draft based on critique"

        # 5. Optimize tokens if large saving available
        token_pct = state.get("token_saving_pct")
        if token_pct is None:
            return "TokenOptimizer", "Attempt token optimisation"

        # If we already had too much savings, skip token optimization
        if "tokens_saved" in state and state.get("log", "").find("exceeds") != -1:
            return None, "All criteria met - skipping token optimization due to excessive savings"

        # Otherwise, run token optimization if applicable
        if token_pct > 15:
            return "TokenOptimizer", "Attempt token optimisation"

        # 6. All criteria met
        return None, "All criteria met"

    # ------------------------------------------------------------------
    def run(self, task_description: str) -> tuple[dict[str, Any], list[dict[str, Any]]]:
        """Execute orchestration loop.

        Returns final *state* and *history* list.
        """
        state: dict[str, Any] = {
            "task": task_description,
            "target_score": self.target_score,
        }
        history: list[dict[str, Any]] = []

        for turn in range(1, self.max_turns + 1):
            role_name, reason = self._policy(state)
            if role_name is None:
                state["termination_reason"] = reason
                break
            role = self.roles.get(role_name)
            if role is None:
                raise KeyError(f"Role '{role_name}' not found. Ensure it exists in roles package.")

            output = role.eval(state)
            state.update(output.to_state_update())
            # Track the last role executed to avoid potential infinite loops
            state["_last_role"] = role_name

            history.append({
                "turn": turn,
                "role": role_name,
                "reason": reason,
                "output_log": state.get("log", ""),
            })

            # Clear single-use log to avoid clutter
            state.pop("log", None)

        return state, history
