"""High-level API for external callers."""

from __future__ import annotations

from typing import Any

from .orchestrator import DEFAULT_MAX_TURNS, DEFAULT_TARGET_SCORE, Orchestrator


def run_orchestrator(task_description: str, *, target_score: float = DEFAULT_TARGET_SCORE, max_turns: int = DEFAULT_MAX_TURNS) -> tuple[dict[str, Any], list[dict[str, Any]]]:
    """Run the default orchestrator for *task_description* and return (*state*, *history*)."""
    orchestrator = Orchestrator(target_score=target_score, max_turns=max_turns)
    return orchestrator.run(task_description)
