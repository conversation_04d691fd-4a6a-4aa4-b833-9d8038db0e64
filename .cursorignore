# Add directories or file patterns to ignore during indexing (e.g. foo/ or *.csv)
.pytest_cache
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
# .vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
# *.log
# logs/

# Temporary files
# .tmp/
# *.tmp
# .temp/
# *.temp

# Data files
*.csv
# *.json
# *.parquet
*.pkl
*.pickle

# Model files
*.model
*.weights
*.ckpt

# Archives
*.zip
*.tar.gz
*.rar

# Node modules (if any JS tooling)
node_modules/

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover

# Jupyter
.ipynb_checkpoints/
