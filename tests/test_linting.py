#!/usr/bin/env python3
"""Test file to verify linting rules are working correctly.

This file intentionally contains code that should trigger various lint rules
to ensure our configuration is working as expected.
"""

from pydantic import BaseModel


# Test 1: McCabe complexity (C901) - max-complexity = 10
def test_mccabe_complexity_violation():
    """This function has too much nested complexity."""
    if True:
        if True:
            if True:
                if True:
                    if True:
                        if True:
                            if True:
                                if True:
                                    if True:
                                        if True:
                                            if True:  # 11 levels deep - should trigger C901
                                                return "too complex"
    return "ok"


# Test 2: Too many statements (PLR0915) - max-statements = 50
def test_too_many_statements():
    """This function has too many statements."""
    a1 = 1
    a2 = 2
    a3 = 3
    a4 = 4
    a5 = 5
    a6 = 6
    a7 = 7
    a8 = 8
    a9 = 9
    a10 = 10
    a11 = 11
    a12 = 12
    a13 = 13
    a14 = 14
    a15 = 15
    a16 = 16
    a17 = 17
    a18 = 18
    a19 = 19
    a20 = 20
    a21 = 21
    a22 = 22
    a23 = 23
    a24 = 24
    a25 = 25
    a26 = 26
    a27 = 27
    a28 = 28
    a29 = 29
    a30 = 30
    a31 = 31
    a32 = 32
    a33 = 33
    a34 = 34
    a35 = 35
    a36 = 36
    a37 = 37
    a38 = 38
    a39 = 39
    a40 = 40
    a41 = 41
    a42 = 42
    a43 = 43
    a44 = 44
    a45 = 45
    a46 = 46
    a47 = 47
    a48 = 48
    a49 = 49
    a50 = 50
    a51 = 51  # Statement 51 - should trigger PLR0915
    return a51


# Test 3: Brittle code philosophy - these should NOT trigger warnings
def test_brittle_code_allowed():
    """Test that brittle code patterns are allowed."""
    # S101 - Assert statements should be allowed
    assert True, "Assertions are allowed for brittle code"
    
    # T201 - Print statements should be allowed
    print("Print statements are allowed")
    
    # E402 - Import order should be flexible (especially in notebooks)
    # BLE001 - Blind exceptions should be caught by linter (not ignored)
    # TRY203 - Re-raising exceptions should be allowed
    
    return "brittle code patterns work"


# Test 4: Too many branches (PLR0912) - max-branches = 12
def test_too_many_branches():
    """This function has too many branches."""
    x = 1
    if x == 1:
        result = "one"
    elif x == 2:
        result = "two"
    elif x == 3:
        result = "three"
    elif x == 4:
        result = "four"
    elif x == 5:
        result = "five"
    elif x == 6:
        result = "six"
    elif x == 7:
        result = "seven"
    elif x == 8:
        result = "eight"
    elif x == 9:
        result = "nine"
    elif x == 10:
        result = "ten"
    elif x == 11:
        result = "eleven"
    elif x == 12:
        result = "twelve"
    elif x == 13:  # Branch 13 - should trigger PLR0912
        result = "thirteen"
    else:
        result = "other"
    return result


# Test 5: Too many arguments (PLR0913) - max-args = 7
def test_too_many_arguments(arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8):  # 8 args - should trigger PLR0913
    """This function has too many arguments."""
    return arg1 + arg2 + arg3 + arg4 + arg5 + arg6 + arg7 + arg8


# Test 6: Pydantic model with too many public methods (PLR0904) - max-public-methods = 20
class TestModelTooManyMethods(BaseModel):
    """This model has too many public methods."""
    value: str = "test"
    
    def method1(self): return 1
    def method2(self): return 2
    def method3(self): return 3
    def method4(self): return 4
    def method5(self): return 5
    def method6(self): return 6
    def method7(self): return 7
    def method8(self): return 8
    def method9(self): return 9
    def method10(self): return 10
    def method11(self): return 11
    def method12(self): return 12
    def method13(self): return 13
    def method14(self): return 14
    def method15(self): return 15
    def method16(self): return 16
    def method17(self): return 17
    def method18(self): return 18
    def method19(self): return 19
    def method20(self): return 20
    def method21(self): return 21  # Method 21 - should trigger PLR0904


if __name__ == "__main__":
    print("This file is meant to be checked by ruff, not executed!")
    print("Run: ruff check tests/test_linting.py")