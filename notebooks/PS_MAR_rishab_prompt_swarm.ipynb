{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import nest_asyncio\n", "\n", "nest_asyncio.apply()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## **Prompt**"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "TODO: Prompt template variables working?\n", "TODO: use .append or similar for formatting, load syntax highlighter.\n", "\"\"\"\n", "\n", "ENHANCED_INITIAL_INSTRUCTIONS_PROMPT_TEMPLATE = \"\"\"\n", "  You are tasked with writing instructions for a prompt engineer who will create a classification system.\n", "  The goal is to provide clear, comprehensive guidance that will enable the engineer to develop an effective classification prompt.\n", "\n", "  -------------------------------------------------------------------------------------\n", "  Consider the following inputs:\n", "\n", "  # CONTEXTUAL INSTRUCTIONS:\n", "  Your CLASSIFICATION_PROMPT should fulfill the following request:\n", "  <classification_instructions>\n", "  & CLASSIFICATION_INSTRUCTIONS & \n", "  </classification_instructions>\n", "\n", "  # CLASSIFICATION TYPE\n", "  <classification_type>\n", "  & CLASSIFICATION_TYPE & \n", "  </classification_type>\n", "\n", "  # CATEGORIES\n", "  <categories>\n", "  & CATEGORIES_PROVIDE_OR_SUGGEST & \n", "  </categories>\n", "\n", "  # INPUT VARIABLES\n", "  <input_variables>\n", "  & PROMPT_VARIABLES_SECTION & \n", "  </input_variables>\n", "  -------------------------------------------------------------------------------------\n", "\n", "    Follow these steps to create your instructions:\n", "\n", "  - all of the inputs are purely text and it's exam conditions.\n", "\n", "  ## Core Task\n", "  1. <PERSON><PERSON> by clearly defining the classification task.\n", "  - **Classify text inputs** into predefined categories  \n", "  - **Type**: Single-label classification (choose one best category)  \n", "  - **CLASSES**: Use these primary labels (add others as needed):  \n", "    - [CLASS 1]  \n", "    - [CLASS 2]  \n", "  etc\n", "    - [Unclassifiable] (for ambiguous cases)  \n", "  - **Exclude**: Off-topic, spam, or non-text inputs.  \n", "\n", "  ## Inputs Required  \n", "  - Text content (required)  \n", "  - Contextual guidelines (optional, e.g., industry-specific rules)\n", "\n", "  ## Inputs Required: Minimum set\n", "  // list the above combined into as few groups as possible.\n", "  // Assume that inputs from multiple sources (e.g., not from an LLM output) can't be combined.\n", "  // single scentence explain why they couldn't be comvbined\n", "\n", "  ## Context  \n", "  - **Domain terms**: Define specialized terms (e.g., \"mixed-intent\" = content with multiple purposes).  \n", "  - **End-use**: Results will [describe purpose, e.g., \"filter support tickets\"].  \n", "  - **Audience**: [Specify, e.g., \"customer service teams\"].  \n", "\n", "  ## Examples  \n", "  ### ✅ Clear Examples  \n", "  - **Category 1**: [CLASSIFICATION (e.g., EMAIL or HARMFUL): Example 1 (text)] \n", "      - [why you chose this as a good demonstration] \n", "  - **Category 2**: [CLASSIFICATION: Example 2]  \n", "      - [why you chose this as a good demonstration] \n", "      etc\n", "  // list five of these\n", "  ### ⚠️ Borderline Cases  \n", "  - [CLASSIFICATION: Example 3] → Explain why it’s ambiguous.  \n", "      - [why you chose this as a good demonstration] \n", "  // list 3 of these\n", "  ### ❌ Misclassifications (<PERSON>)\n", "  - [CLASSIFICATION: Example 4] → Incorrectly labeled as Category 1 (correct: Category 2).  \n", "  // list 3 of these\n", "\n", "  ## Target success criteria\n", "  - **Success Target**: e.g., ≥90% accuracy in validation tests. (3:1 precision vs recall priority)\n", "  - **Priority**: [Specify hierarchy, e.g., \"Category 1 overrides Category 2 if conflicted\"].  \n", "  - **Confidence**: e.g., Flag outputs with <70% confidence for QA review.\n", "\n", "  ## Edge Cases  \n", "  - **Mixed intent**: Assign the dominant purpose.  \n", "  - **Cultural content**: Label as \"Unclassifiable\" if region-specific.  \n", "  - **Ambiguous info**: Request human review.  \n", "\n", "  ## Final Notes  \n", "  - Keep prompts concise and adaptable. -> please ensure clear english that is easy to understand. \n", "  - Be concise and accurate whilst ensuring the reader can easily understand exactly what you mean. \n", "  - Use plain declarative english where possible and ensure scentences concisely explain WHY in-line.\n", "  - Include all sections above.\n", "  \"\"\"\n", "\n", "MASTER_PROMPT_TEMPLATE = \"\"\"\n", "  #TASK\n", "  You are an AI prompt engineer tasked with generating a CLASSIFICATION_PROMPT (<CLASSIFICATION_PROMPT_OUTPUT>). \n", "  Your role is to create clear and concise instructions for classifying text based on the given parameters, optimized for an LLM to use for accurate and reliable classification.\n", "\n", "  Follow these steps to generate the classification prompt:\n", "  1. Analyse your instructions, step by step (<analysis>) in your <scratchpad>\n", "  2. Evaluate and conclude decisions (<conclusion>) in your <scratchpad>\n", "  3. Use that <analysis> and <conclusion> to write the <CLASSIFICATION_PROMPT_OUTPUT> which follows the OUTPUT_FORMAT given.\n", "  4. Your <CLASSIFICATION_OUTPUT_PROMPT> must demand itself it's own <OUTPUT_FORMAT> in-line with the <OUTPUT_FORMAT_RULES>\n", "\n", "  # CONTEXTUAL INSTRUCTIONS:\n", "  Your CLASSIFICATION_PROMPT should fulfill the following request:\n", "  <classification_instructions>\n", "  & ENHANCED_CLASSIFICATION_INSTRUCTIONS & \n", "  </classification_instructions>\n", "\n", "  # CLASSIFICATION TYPE\n", "  <classification_type>\n", "  & CLASSIFICATION_TYPE & \n", "  </classification_type>\n", "\n", "  # CATEGORIES\n", "  <categories>\n", "  & CATEGORIES_PROVIDE_OR_SUGGEST & \n", "  </categories>\n", "\n", "  # INPUT VARIABLES\n", "  <input_variables>\n", "  & PROMPT_VARIABLES_SECTION & \n", "  </input_variables>\n", "\n", "  # GENERAL INSTRUCTIONS\n", "  -  Your drafted CLASSIFICATION_PROMPT will always have INPUT_VARIABLES which you will always declare in their own section, not as part of a sentence e.g., 'your input will be {{input}}.' Is wrong.\n", "  -  INPUT_VARIABLES should always be formatted within {{}} e.g., ' here is the conversation history: <conversation_history> {{CONVERSATION_HISTORY}} </conversation_history>\n", "  -  Your <CLASSIFICATION_PROMPT_OUTPUT> should follow a similar structure and layout to these instructions.\n", "  Variables should also be declared in words (eg This is you...:) and on new lines.\n", "  -  Reference VARIABLES within text in CAPS and name their section as they are most clearly described. E.g., instead of 'Analyze a formal letter', say 'Analyze a FORMAL_LETTER'. \n", "      Example of how a VARIABLE should then be defined:\n", "      <FORMAL_LETTER>\n", "      {{FORMAL_LETTER}}\n", "      </FORMAL_LETTER}}>\n", "  - avoid using protected variable names in other contexts (not in CAPS): ['prompt template','TODO']\n", "\n", "\n", "  # SITUATIONAL INSTRUCTIONS\n", "  4. Based on the classification type, follow these instructions:\n", "  For open-ended classification:\n", "      - Assign an appropriate category or label to the text based on its content.\n", "      - Provide a brief explanation for your classification.\n", "      - Output your response as [CLASSIFICATION: your classification], followed by your explanation.\n", "\n", "\n", "  #------------------------------------------------------------\n", "\n", "  # OUTPUT_FORMAT_RULES (level 2)\n", "  - strictly adhere to the # OUTPUT_FORMAT.\n", "  - Your main classification prompt should be lengthy (>1500 chars) and be most of the prompt, wrapped in <output_classification_prompt> xml tags.\n", "  - Always refer to variables or sections using the verbatim header string E.g., 'the #TASK' or 'analyse the <CLASSIFICATION_OUTPUT>'\n", "  - you MUST think through all considerations in your <ANALYSIS> section then <CONCLUSION> before writing the <CLASSIFICATION_PROMPT_OUTPUT>\n", "  - you MUST include the <CLASSIFICATION_PROMPT_OUTPUT> section:\n", "\n", "  ## OUTPUT_FORMAT (level 2)\n", "  The below is the format your output must be in.\n", "\n", "  <SCRATCHPAD>\n", "\n", "  [First, your step-by-step workings (<analysis> etc) goes here. Make sure to leave enough tokens for your prompt output but DO DO the analysis first:]\n", "  <ANALYSIS>\n", "  [Work through your instrutions thoroughly as a chain-of-thought.]\n", "  [Here is an example of the classification input text to help guide your thinking:\n", "  \"& EXAMPLE_OF_CLASSIFICATION_INPUT_TEXT &\"]\n", "  </ANALYSIS>\n", "  <CONCLUSION>\n", "  [concisely evaluate and make any decisions required to proceed. Explicitly and verbatim reference your instructions and context to explain yourself.']\n", "  <CONCLUSION>\n", "\n", "  </SCRATCHPAD>\n", "\n", "  -----------------END OF SCRATCHPAD---------------------- [always include this divider in your output]\n", "\n", "  <CLASSIFICATION_PROMPT_OUTPUT>\n", "  >[ YOUR CLASSIFICATION_PROMPT_OUTPUT should be similarly structured to this parent prompt, and instruct a chain-of-thought in the child output format.]\n", "\n", "  > # TASK\n", "  > [Task Overview, persona, variable inputs etc, reference what PROMPT_VARIABLES will be provided and how they will be used.]\n", "  > [TASK should include numbered steps required to achieve the goal]\n", "  > [TASK should mention the VARIBLES names' in caps and fluidly in the context of the task.]\n", "  >\n", "  > ## VARIABLES\n", "  > [instroduce the PROMPT_VARIABLES and surround each in named <xml> tags]\n", "  > [declare each variable, e.g., the CONTENT you will analyze is:\n", "  > <CONTENT>\n", "  > {{CONTENT}} etc]\n", "  >\n", "  > ## GENERAL INSTRUCTIONS\n", "  > [general, numbered steps required to achieve the goal goes here.]\n", "  >\n", "  > ## SITUATIONAL INSTRUCTIONS:\n", "  > [instructions specific to a classification or specific situation the model may come across]\n", "  >\n", "  > ## ADDITIONAL INSTRUCTIONS:\n", "  > [Left blank for future use. just put: 'add additional instructions here'.\n", "  >\n", "  > <OUTPUT_FORMAT2>\n", "  > #OUTPUT_FORMAT2\n", "  >> [include this line verbatim: 'You must strictly follow the OUTPUT_FORMAT2, including all <sections>']\n", "  >> <scratchpad>\n", "  >> <analysis> [put guidance and boolean rubric checklist here] </analysis>\n", "  >> <conclusion> [put your guidance here and include this line verbatim: 'explicitly and verbatim reference your instructions and context to explain yourself']  </conclusion>\n", "  >> </scratchpad>\n", "  >>\n", "  >> <classification>\n", "  >> [put guidance here. This should always be ONLY the classification decision itself.]\n", "  >> </classification>\n", "  >>\n", "  >> <category/categories>\n", "  >> [put guidance here. If categories (plural): Should be one per new line]\n", "  >> </category/categories>\n", "  >>\n", "  >> <justification> [Include this line verbatim: 'Brief explanation of classification decision, explicitly and verbatim reference your instructions and context to explain yourself.' [+additional guidance if required]] </justification>\n", "  >\n", "  > # LONG INPUT VARIABLES\n", "  > [if any, long input variables go here, in {{}} and declared first with words as above]\n", "  >\n", "  > Remember to ... [Always give final confirmation of the classification task in question.\n", "  > [include this verbatim: 'Now, start your response with <SCRATCHPAD><ANALYSIS>']\n", "  >\n", "  > </OUTPUT_FORMAT2> [close this tag here before final instruction notes]\n", "\n", "  </CLASSIFICATION_PROMPT_OUTPUT>\n", "\n", "  #FINAL INSTRUCTION NOTES [always include these verbatim as below]\n", "\n", "  Remember to analyze the text carefully and provide a thoughtful classification based on the given parameters.\n", "  Remember, your primary goal is to generate diverse and contextually appropriate values that will lead to varied and meaningful test cases for the given prompt.\n", "\n", "  Now, start your response with <SCRATCHPAD><ANALYSIS>\n", "\n", "\"\"\"\n", "\n", "INSPIRATION_SEED_GENERATOR_PROMPT1 = \"\"\"\n", "  You are a Test Case Inspiration Generator.\n", "  1. Your task is to generate diverse test cases relevant to given instructions, examples, and a prompt template.\n", "  2. These test cases should be single sentences or single paragraphs (if required) and should vary in structure, wording, and category to ensure diversity.\n", "  3. The instructions will also include a list of words/phrases to avoid. Do not use these words/phrases in your test cases.\n", "  4. The instructions will also include number of instance of each required category. \n", "    - Carefully understand the instructions to generate the required number of test cases for each category.\n", "    - If you can't find any specified number of instances, generate as many as you can.\n", "\n", "  First, carefully read and understand the following instructions:\n", "  <instructions>\n", "  & CLASSIFICATION_INSTRUCTIONS &\n", "  </instructions>\n", "\n", "  Now, consider these examples as a reference for the style and format of test cases:\n", "  <examples>\n", "  & EXAMPLES_OF_THE_TASK &\n", "  </examples>\n", "\n", "  The test cases you generate should be of the following type:\n", "  <category>\n", "  & TEST_CASE_GENERATION_LOGIC &\n", "  </category>\n", "\n", "  Avoid duplicating or closely resembling any test cases from this list:\n", "  <avoid_dups>\n", "  & INSPIRATION_SEEDS &\n", "  </avoid_dups>\n", "\"\"\"\n", "\n", "INSPIRATION_SEED_GENERATOR_PROMPT2 = \"\"\"\n", "  When generating test cases, follow these guidelines:\n", "  1. Output prose without labels.\n", "  2. Vary structure, wording, and category to ensure a diverse set of test cases.\n", "  3. Use the provided examples as inspiration for variety.\n", "  4. Avoid superfluous prose, conjunctive adverbs, transition words, discourse markers, and introductory or concluding statements.\n", "  5. Do not use the following words/phrases: delve, intricate, in summary, underscore, important to note, language model, explore, captivate, quantum, black hole, crypto, tapestry, leverage, embrace, dynamic, resonate, testament, elevate, pitfalls, comprehensive, multifaceted, uncharted, highly, ultimately, dramatically, embark on a journey, treasure trove, digital world, realm.\n", "\n", "  Before generating the test cases, use the <scratchpad> to think through your approach:\n", "  <scratchpad>\n", "  - Analyze the given instructions and examples\n", "  - Consider the prompt template and how it can be applied\n", "  - Think about diverse scenarios within the specified category\n", "  - Plan how to create varied and unique test cases\n", "  - Ensure compliance with the guidelines and avoid using prohibited words/phrases\n", "  </scratchpad>\n", "\n", "  Now, generate the test cases. Each test case should be a single sentence or paragraph, depending on the complexity required.\n", "\n", "  FORMAT YOUR RESPONSE AS:\n", "  <test_case_inspiration_seeds>\n", "  {\n", "    \"category_1\": {\n", "        \"subcategory_1\": [\n", "            \"test case 1\",\n", "            \"test case 2\",\n", "            ...\n", "        ],\n", "        \"subcategory_2\": [\n", "            \"test case 1\",\n", "            \"test case 2\",\n", "            ...\n", "        ],\n", "        ...\n", "    },\n", "    \"category_2\": {\n", "        \"subcategory_1\": [\n", "            \"test case 1\",\n", "            \"test case 2\",\n", "            ...\n", "        ],\n", "        ...\n", "    },\n", "    ...\n", "  }\n", "  </test_case_inspiration_seeds>\n", "\n", "  EXAMPLE RESPONSE:\n", "  <test_case_inspiration_seeds>\n", "  {\n", "    \"straightforward_examples\": {\n", "        \"matching\": [\n", "            \"10pm tomorrow for meeting?\",\n", "            \"do you have time to speak to<PERSON><PERSON>?\"\n", "        ],\n", "        \"non_matching\": [\n", "            \"hey, where is the book I lent you?\"\n", "        ]\n", "    },\n", "    \"hard_examples\": {\n", "        \"matching\": [],\n", "        \"non_matching\": [\n", "            \"Did you see <PERSON> today at 4pm?\"\n", "        ]\n", "    },\n", "    \"edge_cases\": {\n", "        \"calendar_invites\": [\n", "            \"auto email from gcal inviting you to event\",\n", "            \"auto email from events company inviting you to event\", \n", "            \"human reply to your outbound gcal event invite\"\n", "        ]\n", "    }\n", "  }\n", "  </test_case_inspiration_seeds>\n", "\n", "  IMPORTANT: Format your actual response exactly as shown above, with no additional text before or after.\n", "\n", "  now, start your response with <scratchpad> and end with </test_case_inspiration_seeds>\n", "\"\"\"\n", "\n", "TEST_CASE_GENERATOR_PROMPT = \"\"\"\n", "  You are an AI assistant tasked with generating diverse and appropriate values for variables in a given prompt template. \n", "  Your goal is to create realistic, contextual, and specific yet generalizable values that fit the requirements of the template.\n", "  Here is the prompt template you will be working with:\n", "  -------------------------------------------------------------------------------------\n", "  <prompt_template>\n", "      \" & MASTER_PROMPT & \"\n", "  </prompt_template>\n", "  -------------------------------------------------------------------------------------\n", "  Here is an example input for the prompt template's variables.\n", "  <EXAMPLE_INPUTS>\n", "      \" & EXAMPLES_OF_THE_TASK & \"\n", "  </EXAMPLE_INPUTS>\n", "\n", "  You will be generating values for the following variable:\n", "  <variable_name>\n", "      \" & PROMPT_VARIABLE_SECTION & \"\n", "  </variable_name>\n", "\n", "  <OUTPUT_FORMAT>\n", "  Before generating the final value, chain-of-thought output the following analysis in <analysis> tags. \n", "  This analysis will help you create a more appropriate and contextual value.\n", "\n", "  <scratchpad>\n", "  <analysis>\n", "  1. Summary of prompt template:\n", "    - Briefly describe the purpose of the template\n", "    - Outline its main components and structure\n", "    - Identify any specific requirements or constraints\n", "  2. Key points from example inputs:\n", "    - List 3-4 notable patterns or characteristics from the examples\n", "    - Highlight any unique or recurring elements\n", "    - Note the tone, style, or format used in the examples\n", "  3. Analysis of variable name:\n", "    - Break down the variable name into its components\n", "    - Infer who likely writes the variable. A user? A bot? From a website? etc\n", "    - Infer the expected type of value (e.g., text, number, date)\n", "    - Consider any implied characteristics or qualities\n", "  4. Inspiration seed interpretation:\n", "    - Identify key themes or concepts from the seed\n", "    - List potential ways to incorporate the seed into the value\n", "    - Consider how the seed might influence the tone or style of the value\n", "  5. Considerations for value generation:\n", "    - Realism: List 2-3 ways to ensure the value feels authentic and plausible\n", "    - Context: Describe how the value should relate to other elements in the template\n", "    - Specificity vs. Generalizability: Propose a balance between unique details and broad applicability\n", "    - Descriptiveness: Outline strategies to make the value rich and informative\n", "  6. Potential edge cases or limitations:\n", "    - Identify 2-3 possible challenges in generating an appropriate value\n", "    - Propose solutions or workarounds for each challenge\n", "  7. Brainstorming potential values:\n", "    - Generate 3-4 rough drafts of possible values\n", "    - Do not drift from the object in the inspiration seed as the other vars will draw from it and they need to match up. [note: 14/1/25 due to drift]\n", "    - Briefly evaluate the strengths and weaknesses of each draft\n", "  </analysis>\n", "  <conclusion>\n", "  Summarize your analysis, highlighting the key insights that will guide your value generation. \n", "  Describe the characteristics of the ideal value based on your analysis, and explain which of your brainstormed options best meets these criteria.\n", "  </conclusion>\n", "  </scratchpad>\n", "\n", "  Now, generate a value that adheres to the following requirements:\n", "  1. The value must be realistic and contextually appropriate.\n", "  2. It should be specific but generalizable.\n", "  3. The value must be meaningful within the context of the prompt template.\n", "  4. Ensure the value is descriptive (at least 10 characters long).\n", "  5. Write numbers as descriptive text.\n", "  6. Do not use placeholder or sample values.\n", "  7. The value must be a properly escaped XML string.\n", "\n", "  After your <analysis> section, generate the value and present it in the following format (Where VARIABLE_NAME is replaced with the relevant VARIABLE_NAME provided above:\n", "  <{{VARIABLE_NAME}}>[Your generated value here]</{{VARIABLE_NAME}}>\n", "  </OUTPUT_FORMAT>\n", "\n", "  To inspire your value generation, consider the following seed:\n", "  1. Use this inspiration_seed to guide the direction of your generated value. \n", "  2. Your value should incorporate direction from the insiration_seed.\n", "  3. The inspiration seed contains a category and subcategory. \n", "  4. Your value should be relevant to the category and subcategory.\n", "  <inspiration_seed>\n", "      \" & INSPIRATION_SEED & \"\n", "  </inspiration_seed>\n", "  \n", "  Remember, your goal is to generate a value that is realistic, contextual, and appropriate for the given prompt template and variable name. \n", "  Do not include any explanations or additional text outside of the specified XML tags.\n", "  Your response must start <scratchpad><analysis> and should end with <\"&variableName&\"> [ content ] </\"&variableName&\">\n", "\"\"\"\n", "\n", "TEST_CASE_VALIDATOR_PROMPT = \"\"\"\n", "  You are an AI assistant tasked with validating the quality of test cases generated by an LLM.\n", "\n", "  Here are the information you will be working with:\n", "   - The compiled prompt template contains the master prompt with the variables values filled in.\n", "   - The inspiration seed contains the value of the inspiration seed and it's category and subcategory.\n", "  -------------------------------------------------------------------------------------\n", "  <compiled_prompt_template>\n", "      \" & COMPILED_MASTER_PROMPT & \"\n", "  </compiled_prompt_template>\n", "\n", "  <inspiration_seed>\n", "      \" & INSPIRATION_SEED & \"\n", "  </inspiration_seed>\n", "  -------------------------------------------------------------------------------------\n", "  Here is the test case output you will be validating:\n", "  <test_case_output>\n", "      \" & TEST_CASE_OUTPUT & \"\n", "  </test_case_output>\n", "\n", "  Now, generate a value that adheres to the following requirements:\n", "  1. Your response should be a boolean value (true or false) indicating whether the test case output is valid or not.\n", "  2. The validation is true if the test_case_output result matches the category and subcategory of the inspiration seed.\n", "  3. The validation is false if the test_case_output result does not match the category and subcategory of the inspiration seed.\n", "  4. Think through your response step by step in your <scratchpad> and <conclusion> tags.\n", "  5. The conclusion must not contain any text other than the boolean value.\n", "\n", "  Format your response as follows:\n", "  <scratchpad>\n", "      <analysis>\n", "      [Your step-by-step analysis goes here]\n", "      </analysis>\n", "\n", "      <conclusion>\n", "      [Your conclusion goes here]\n", "      </conclusion>\n", "  </scratchpad>\n", "\n", "  Example response:\n", "  <scratchpad>\n", "    <analysis>\n", "    The category and subcategory of the inspiration seed is \"calendar_invites\" and \"gcal\".\n", "    The test case output is \"auto email from gcal inviting you to event\".\n", "    The test case output is valid because it matches the category and subcategory of the inspiration seed.\n", "    </analysis>\n", "\n", "    <conclusion>\n", "    true\n", "    </conclusion>\n", "  </scratchpad>\n", "\"\"\"\n", "\n", "\n", "ENHANCED_TEST_CASE_GENERATION_LOGIC_PROMPT_TEMPLATE = \"\"\"\n", "  You are tasked with writing test cases generation instructions for a prompt engineer who will create a classification system.\n", "  The goal is to provide clear, comprehensive guidance that will enable robust test cases generation using LLM.\n", "\n", "  -------------------------------------------------------------------------------------\n", "  Consider the following inputs:\n", "\n", "  # CONTEXTUAL INSTRUCTIONS:\n", "  Your CLASSIFICATION_PROMPT should fulfill the following request:\n", "  <classification_instructions>\n", "  & ENHANCED_INITIAL_INSTRUCTIONS & \n", "  </classification_instructions>\n", "\n", "  # CLASSIFICATION TYPE\n", "  <classification_type>\n", "  & CLASSIFICATION_TYPE & \n", "  </classification_type>\n", "\n", "  # CATEGORIES\n", "  <categories>\n", "  & CATEGORIES_PROVIDE_OR_SUGGEST & \n", "  </categories>\n", "\n", "  # INPUT VARIABLES\n", "  <input_variables>\n", "  & PROMPT_VARIABLES_SECTION & \n", "  </input_variables>\n", "\n", "  <test_case_generation_logic>\n", "  & TEST_CASE_GENERATION_LOGIC & \n", "  </test_case_generation_logic>\n", "  -------------------------------------------------------------------------------------\n", "\n", "  \"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## **LLM**"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["class LLM:\n", "    \"\"\"\n", "    Class containing constants for various LLM models.\n", "\n", "    This class provides a centralized location for storing model identifiers,\n", "    making it easier to manage and update supported models across the project.\n", "    \"\"\"\n", "\n", "    grok0626 = \"grok-0626.research-models\"  # model 1\n", "    grok0701 = \"grok-0701.research-models\"  # model 2\n", "\n", "    # Claude <PERSON>\n", "    claude_3_5_sonnet = \"claude-3-5-sonnet-20240620\"\n", "    claude_3_haiku = \"claude-3-haiku-20240307\"\n", "    claude_3_opus = \"claude-3-opus-20240229\"\n", "    claude_3_sonnet = \"claude-3-sonnet-20240229\"\n", "    claude_3_7_sonnet = \"anthropic/claude-3-7-sonnet-20250219\"\n", "\n", "    # OpenAI Models\n", "    gpt_3_5_turbo_instruct = \"gpt-3.5-turbo-instruct\"\n", "    gpt_3_5_turbo = \"openai/gpt-3.5-turbo\"\n", "    gpt_4o = \"gpt-4o\"\n", "    gpt_4o_mini = \"gpt-4o-mini\"\n", "    gpt_4_turbo = \"gpt-4-turbo\"\n", "    gpt_4_vision_preview = \"gpt-4-vision-preview\"\n", "    gpt_4 = \"gpt-4\"\n", "\n", "    # Google Gemini Models\n", "    gemini_pro_completion = \"gemini/gemini-pro\"\n", "    gemini_1_5_pro_latest = \"gemini/gemini-1.5-pro-latest\"\n", "    gemini_1_5_flash = \"gemini/gemini-1.5-flash\"\n", "    # gemini_pro_vision = \"gemini-pro-vision\"  # requires google-cloud-aiplatform; outdated\n", "\n", "    # Perplexity Models\n", "    pplx_llama_3_sonar_small_32k_online = \"perplexity/llama-3-sonar-small-32k-online\"\n", "    pplx_llama_3_sonar_small_32k_chat = \"perplexity/llama-3-sonar-small-32k-chat\"\n", "    pplx_llama_3_sonar_large_32k_online = \"perplexity/llama-3-sonar-large-32k-online\"\n", "    pplx_llama_3_sonar_large_32k_chat = \"perplexity/llama-3-sonar-large-32k-chat\"\n", "    pplx_llama_3_8b_instruct = \"perplexity/llama-3-8b-instruct\"\n", "    pplx_llama_3_70b_instruct = \"perplexity/llama-3-70b-instruct\"\n", "    pplx_mixtral_8x7b_instruct = \"perplexity/mixtral-8x7b-instruct\"\n", "\n", "    # Mistral Models\n", "    mistral_7b_instruct_perplexity = \"perplexity/mistral-7b-instruct\"\n", "    mistral_small = \"mistral/mistral-small-latest\"\n", "    mistral_medium = \"mistral/mistral-medium-latest\"\n", "    mistral_large = \"mistral/mistral-large-latest\"\n", "    mistral_7b = \"mistral/open-mistral-7b\"\n", "    mixtral_8x7b = \"mistral/open-mixtral-8x7b\"\n", "    mixtral_8x22b = \"mistral/open-mixtral-8x22b\"\n", "    codestral = \"mistral/codestral-latest\"\n", "\n", "    # Fireworks Models (Newest)\n", "    fire_llama_v31_405b_instruct = \"fireworks_ai/llama-v3p1-405b-instruct\"\n", "    fire_llama_v31_70b_instruct = \"fireworks_ai/llama-v3p1-70b-instruct\"\n", "    fire_llama_v31_8b_instruct = \"fireworks_ai/llama-v3p1-8b-instruct\"\n", "\n", "    # Fireworks Models\n", "    fire_deepseek_r1 = \"fireworks_ai/deepseek-r1\"\n", "    firellava_13b = \"fireworks_ai/firellava-13b\"\n", "    firefunction_v1 = \"fireworks_ai/firefunction-v1\"\n", "    mixtral_8x7b_instruct_fireworks = \"fireworks_ai/mixtral-8x7b-instruct\"\n", "    llama_v3_70b_instruct_fireworks = \"fireworks_ai/llama-v3-70b-instruct\"\n", "    llama_v3_70b_instruct_hf_fireworks = \"fireworks_ai/llama-v3-70b-instruct-hf\"\n", "    llama_v3_8b_instruct_fireworks = \"fireworks_ai/llama-v3-8b-instruct\"\n", "    llama_v3_8b_instruct_hf_fireworks = \"fireworks_ai/llama-v3-8b-instruct-hf\"\n", "    llama_v3_1_70b_instruct_fireworks = (\n", "        \"accounts/fireworks/models/llama-v3p1-70b-instruct\"\n", "    )\n", "    mixtral_8x7b_instruct_hf_fireworks = \"fireworks_ai/mixtral-8x7b-instruct-hf\"\n", "    mythomax_l2_13b_fireworks = \"fireworks_ai/mythomax-l2-13b\"\n", "    phi_3_vision_128k_instruct_fireworks = \"fireworks_ai/phi-3-vision-128k-instruct\"\n", "    qwen2_72b_instruct_fireworks = \"fireworks_ai/qwen2-72b-instruct\"\n", "\n", "    # Cohere\n", "    cohere_command = \"command\"\n", "    cohere_command_light = \"command-light\"\n", "    cohere_command_r = \"command-r\"\n", "    cohere_command_r_plus = \"command-r-plus\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## **Iteration Page Events**"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["\"\"\"Module for generating and managing the master prompt template final output.\"\"\"\n", "\n", "from dataclasses import dataclass\n", "\n", "\n", "@dataclass\n", "class MasterPromptTemplateFinalConfig:\n", "    \"\"\"Configuration container for master prompt generation.\n", "\n", "    Attributes:\n", "        filled_master_prompt_template: Filled master prompt template\n", "    \"\"\"\n", "\n", "    filled_master_prompt_template: str\n", "\n", "\n", "def get_generate_master_prompt_template_final_output(\n", "    config: MasterPromptTemplateFinalConfig,\n", ") -> str:\n", "    \"\"\"Generate the final output for the master prompt template.\n", "\n", "    Args:\n", "        config: Configuration object containing the filled master prompt template\n", "\n", "    Returns:\n", "        The final output for the master prompt template\n", "    \"\"\"\n", "    return config.filled_master_prompt_template"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## **Start Page Events**"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["\"\"\"Module for generating and managing the master prompt configuration.\"\"\"\n", "\n", "from dataclasses import dataclass\n", "\n", "\n", "@dataclass\n", "class EnhancedInitialInstructionsPromptConfig:\n", "    \"\"\"Configuration container for enhanced initial instructions prompt generation.\n", "\n", "    Attributes:\n", "        initial_instructions: Base instructions for the classifier\n", "        classifier_type: Type of classification task\n", "        output_classes: Available output categories/classes\n", "        input_variables: Template variables for input processing\n", "        template: Master template string with placeholders\n", "    \"\"\"\n", "\n", "    initial_instructions: str\n", "    classifier_type: str\n", "    output_classes: str\n", "    input_variables: str\n", "    template: str\n", "\n", "\n", "def get_generate_enhanced_initial_instructions_prompt(\n", "    config: EnhancedInitialInstructionsPromptConfig,\n", ") -> str:\n", "    \"\"\"Generate a complete master prompt by populating template placeholders.\n", "\n", "    Args:\n", "        config: Configuration object containing template and replacement values\n", "\n", "    Returns:\n", "        Fully populated prompt string with all placeholders replaced\n", "\n", "    Example:\n", "        >>> config = MasterPromptConfig(...)\n", "        >>> prompt = generate_master_prompt(config)\n", "    \"\"\"\n", "    return (\n", "        config.template.replace(\n", "            \"& CLASSIFICATION_INSTRUCTIONS &\", config.initial_instructions\n", "        )\n", "        .replace(\"& CLASSIFICATION_TYPE &\", config.classifier_type)\n", "        .replace(\"& CATEGORIES_PROVIDE_OR_SUGGEST &\", config.output_classes)\n", "        .replace(\"& PROMPT_VARIABLES_SECTION &\", config.input_variables)\n", "    )\n", "\n", "\n", "@dataclass\n", "class MasterPromptConfig:\n", "    \"\"\"Configuration container for master prompt generation.\n", "\n", "    Attributes:\n", "        enhanced_initial_instructions: Base instructions for the classifier\n", "        classifier_type: Type of classification task\n", "        output_classes: Available output categories/classes\n", "        input_variables: Template variables for input processing\n", "        input_examples: Example inputs for guidance\n", "        template: Master template string with placeholders\n", "    \"\"\"\n", "\n", "    enhanced_initial_instructions: str\n", "    classifier_type: str\n", "    output_classes: str\n", "    input_variables: str\n", "    input_examples: str\n", "    template: str\n", "\n", "\n", "def get_generate_master_prompt(config: MasterPromptConfig) -> str:\n", "    \"\"\"Generate a complete master prompt by populating template placeholders.\n", "\n", "    Args:\n", "        config: Configuration object containing template and replacement values\n", "\n", "    Returns:\n", "        Fully populated prompt string with all placeholders replaced\n", "\n", "    Example:\n", "        >>> config = MasterPromptConfig(...)\n", "        >>> prompt = generate_master_prompt(config)\n", "    \"\"\"\n", "    return (\n", "        config.template.replace(\n", "            \"& ENHANCED_CLASSIFICATION_INSTRUCTIONS &\",\n", "            config.enhanced_initial_instructions,\n", "        )\n", "        .replace(\"& CLASSIFICATION_TYPE &\", config.classifier_type)\n", "        .replace(\"& CATEGORIES_PROVIDE_OR_SUGGEST &\", config.output_classes)\n", "        .replace(\"& PROMPT_VARIABLES_SECTION &\", config.input_variables)\n", "        .replace(\"& EXAMPLE_OF_CLASSIFICATION_INPUT_TEXT &\", config.input_examples)\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## **Test Case Events**"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["\"\"\"Module for test case generation prompt configuration and processing.\"\"\"\n", "\n", "from dataclasses import dataclass\n", "\n", "from loguru import logger\n", "\n", "\n", "@dataclass\n", "class EnhancedTestCasesGenerationLogicConfig:\n", "    \"\"\"Configuration container for inspiration seed generation prompts.\n", "\n", "    Attributes:\n", "        enhanced_initial_instructions: Base classification instructions\n", "        classifier_type: Type of classification task\n", "        output_classes: Available output categories/classes\n", "        input_variables: Template variables for input processing\n", "        test_case_generation_logic: Test case generation logic\n", "        template: Template string with placeholders\n", "        placeholders: Optional custom placeholder replacements\n", "    \"\"\"\n", "\n", "    enhanced_initial_instructions: str\n", "    template: str\n", "    test_case_generation_logic: str\n", "    classifier_type: str\n", "    output_classes: str\n", "    input_variables: str\n", "    # Define standard placeholders but allow overrides\n", "    placeholders: dict[str, str] | None = None\n", "\n", "    def get_placeholder_mapping(self) -> dict[str, str]:\n", "        \"\"\"Get the mapping of placeholders to their values.\"\"\"\n", "        default_placeholders = {\n", "            \"& ENHANCED_INITIAL_INSTRUCTIONS &\": self.enhanced_initial_instructions,\n", "            \"& TEST_CASE_GENERATION_LOGIC &\": self.test_case_generation_logic,\n", "            \"& CLASSIFICATION_TYPE &\": self.classifier_type,\n", "            \"& CATEGORIES_PROVIDE_OR_SUGGEST &\": self.output_classes,\n", "            \"& PROMPT_VARIABLES_SECTION &\": self.input_variables,\n", "        }\n", "        if self.placeholders:\n", "            default_placeholders.update(self.placeholders)\n", "        return default_placeholders\n", "\n", "\n", "def get_enhanced_test_case_generation_logic_prompt(\n", "    config: EnhancedTestCasesGenerationLogicConfig,\n", ") -> str:\n", "    \"\"\"Generate complete inspiration seed generation prompt by populating template placeholders.\n", "\n", "    Args:\n", "        config: Configuration object with template and values\n", "\n", "    Returns:\n", "        Fully populated prompt string\n", "\n", "    Raises:\n", "        ValueError: If template contains unmatched placeholders\n", "\n", "    Example:\n", "        >>> config = InspirationSeedGeneratorPromptConfig(...)\n", "        >>> prompt = generate_inspiration_seed_generator_prompt(config)\n", "    \"\"\"\n", "    result = config.template\n", "    placeholder_mapping = config.get_placeholder_mapping()\n", "\n", "    # Replace all placeholders\n", "    for placeholder, value in placeholder_mapping.items():\n", "        if not value:\n", "            logger.warning(f\"Placeholder {placeholder} has no value\")\n", "            continue\n", "        result = result.replace(placeholder, value)\n", "\n", "    return result\n", "\n", "\n", "@dataclass\n", "class InspirationSeedGeneratorPromptConfig:\n", "    \"\"\"Configuration container for inspiration seed generation prompts.\n", "\n", "    Attributes:\n", "        initial_instructions: Base classification instructions\n", "        inspiration_seeds: Type of test_case instances to generate\n", "        master_prompt: Final prompt used for classification\n", "        template: Template string with placeholders\n", "        placeholders: Optional custom placeholder replacements\n", "    \"\"\"\n", "\n", "    initial_instructions: str\n", "    inspiration_seeds: str\n", "    template: str\n", "    test_case_generation_logic: str\n", "    inspiration_seeds: str\n", "    input_examples: str\n", "    # Define standard placeholders but allow overrides\n", "    placeholders: dict[str, str] | None = None\n", "\n", "    def get_placeholder_mapping(self) -> dict[str, str]:\n", "        \"\"\"Get the mapping of placeholders to their values.\"\"\"\n", "        default_placeholders = {\n", "            \"& CLASSIFICATION_INSTRUCTIONS &\": self.initial_instructions,\n", "            \"& EXAMPLES_OF_THE_TASK &\": self.input_examples,\n", "            \"& TEST_CASE_GENERATION_LOGIC &\": self.test_case_generation_logic,\n", "            \"& INSPIRATION_SEEDS &\": self.inspiration_seeds,\n", "        }\n", "        if self.placeholders:\n", "            default_placeholders.update(self.placeholders)\n", "        return default_placeholders\n", "\n", "\n", "def get_inspiration_seed_generator_prompt(\n", "    config: InspirationSeedGeneratorPromptConfig,\n", ") -> str:\n", "    \"\"\"Generate complete inspiration seed generation prompt by populating template placeholders.\n", "\n", "    Args:\n", "        config: Configuration object with template and values\n", "\n", "    Returns:\n", "        Fully populated prompt string\n", "\n", "    Raises:\n", "        ValueError: If template contains unmatched placeholders\n", "\n", "    Example:\n", "        >>> config = InspirationSeedGeneratorPromptConfig(...)\n", "        >>> prompt = generate_inspiration_seed_generator_prompt(config)\n", "    \"\"\"\n", "    result = config.template\n", "    placeholder_mapping = config.get_placeholder_mapping()\n", "\n", "    # Replace all placeholders\n", "    for placeholder, value in placeholder_mapping.items():\n", "        if not value:\n", "            logger.warning(f\"Placeholder {placeholder} has no value\")\n", "            continue\n", "        result = result.replace(placeholder, value)\n", "\n", "    return result\n", "\n", "\n", "@dataclass\n", "class TestCaseGeneratorPromptConfig:\n", "    \"\"\"Configuration container for test case generation prompts.\n", "\n", "    Attributes:\n", "        master_prompt: Final prompt used for classification\n", "        inspiration_seed: Type of test_case instances to generate\n", "        input_vars: The variable name to generate test cases for\n", "        input_examples: Examples of the task\n", "        template: Prompt template string with placeholders\n", "        placeholders: Optional custom placeholder replacements\n", "    \"\"\"\n", "\n", "    master_prompt: str\n", "    input_examples: str\n", "    inspiration_seed: str\n", "    input_vars: str\n", "    template: str\n", "    # Define standard placeholders but allow overrides\n", "    placeholders: dict[str, str] | None = None\n", "\n", "    def get_placeholder_mapping(self) -> dict[str, str]:\n", "        \"\"\"Get the mapping of placeholders to their values.\"\"\"\n", "        default_placeholders = {\n", "            \"& MASTER_PROMPT &\": self.master_prompt,\n", "            \"& EXAMPLES_OF_THE_TASK &\": self.input_examples,\n", "            \"& PROMPT_VARIABLE_SECTION &\": self.input_vars,\n", "            \"& INSPIRATION_SEED &\": self.inspiration_seed,\n", "        }\n", "        if self.placeholders:\n", "            default_placeholders.update(self.placeholders)\n", "        return default_placeholders\n", "\n", "\n", "def get_test_case_generator_prompt(\n", "    config: TestCaseGeneratorPromptConfig,\n", ") -> str:\n", "    \"\"\"Generate complete test case generation prompt by populating template placeholders.\n", "\n", "    Args:\n", "        config: Configuration object with template and values\n", "\n", "    Returns:\n", "        Fully populated prompt string\n", "\n", "    Raises:\n", "        ValueError: If template contains unmatched placeholders\n", "\n", "    Example:\n", "        >>> config = TestCaseGeneratorPromptConfig(...)\n", "        >>> prompt = generate_test_case_generator_prompt(config)\n", "    \"\"\"\n", "    result = config.template\n", "    placeholder_mapping = config.get_placeholder_mapping()\n", "\n", "    # Replace all placeholders\n", "    for placeholder, value in placeholder_mapping.items():\n", "        if not value:\n", "            logger.warning(f\"Placeholder {placeholder} has no value\")\n", "            continue\n", "        result = result.replace(placeholder, value)\n", "\n", "    return result\n", "\n", "\n", "@dataclass\n", "class TestCaseValidatorPromptConfig:\n", "    \"\"\"Configuration container for test case validation prompts.\n", "\n", "    Attributes:\n", "        compiled_master_prompt: Final prompt used for classification\n", "        test_case_output: The output of the test case\n", "        inspiration_seed: Type of test_case instances to generate\n", "        template: Prompt template string with placeholders\n", "        placeholders: Optional custom placeholder replacements\n", "    \"\"\"\n", "\n", "    compiled_master_prompt: str\n", "    test_case_output: str\n", "    inspiration_seed: str\n", "    template: str\n", "    # Define standard placeholders but allow overrides\n", "    placeholders: dict[str, str] | None = None\n", "\n", "    def get_placeholder_mapping(self) -> dict[str, str]:\n", "        \"\"\"Get the mapping of placeholders to their values.\"\"\"\n", "        default_placeholders = {\n", "            \"& COMPILED_MASTER_PROMPT &\": self.compiled_master_prompt,\n", "            \"& INSPIRATION_SEED &\": self.inspiration_seed,\n", "            \"& TEST_CASE_OUTPUT &\": self.test_case_output,\n", "        }\n", "        if self.placeholders:\n", "            default_placeholders.update(self.placeholders)\n", "        return default_placeholders\n", "\n", "\n", "def get_test_case_validator_prompt(\n", "    config: TestCaseValidatorPromptConfig,\n", ") -> str:\n", "    \"\"\"Generate complete test case validation prompt by populating template placeholders.\n", "\n", "    Args:\n", "        config: Configuration object with template and values\n", "\n", "    Returns:\n", "        Fully populated prompt string\n", "\n", "    Raises:\n", "        ValueError: If template contains unmatched placeholders\n", "\n", "    Example:\n", "        >>> config = TestCaseValidatorPromptConfig(...)\n", "        >>> prompt = generate_test_case_validator_prompt(config)\n", "    \"\"\"\n", "    result = config.template\n", "    placeholder_mapping = config.get_placeholder_mapping()\n", "\n", "    # Replace all placeholders\n", "    for placeholder, value in placeholder_mapping.items():\n", "        if not value:\n", "            logger.warning(f\"Placeholder {placeholder} has no value\")\n", "            continue\n", "        result = result.replace(placeholder, value)\n", "\n", "    return result"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## **ParallelLLMDataFrameProcessor**"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["\"\"\"Contains the ParallelLLMDataFrameProcessor class for processing dataframes with LLM chains.\"\"\"\n", "\n", "# Standard library imports\n", "import asyncio\n", "import string\n", "import time\n", "import traceback\n", "from concurrent.futures import ThreadPoolExecutor\n", "from typing import NamedTuple\n", "from uuid import uuid4\n", "\n", "# Third-party imports\n", "import litellm\n", "import pandas as pd\n", "import tenacity\n", "from asyncio_throttle import Throttler\n", "\n", "\n", "class ChainStep(NamedTuple):\n", "    \"\"\"\n", "    Contains the ChainStep class for defining steps in an LLM processing chain.\n", "\n", "    This module defines a NamedTuple class `ChainStep` that represents a single step\n", "    in a chain of LLM processing operations. It encapsulates various parameters\n", "    needed for executing an LLM query, such as the prompt template, model settings,\n", "    and output specifications.\n", "\n", "    Represents a single step in an LLM processing chain.\n", "\n", "    This class defines the structure and parameters for each step in the chain,\n", "    including the prompt template, model settings, and output specifications.\n", "\n", "    Classes:\n", "        ChainStep: A NamedTuple class representing a step in an LLM processing chain.\n", "\n", "    Attributes:\n", "        pt (str): The prompt template for this step.\n", "        mapping (Optional[Dict[str, str]]): A mapping of prompt keywords to column names.\n", "        temperature (Optional[float]): The temperature setting for the LLM model.\n", "        max_tokens (Optional[int]): The maximum number of tokens for the LLM response.\n", "        model (Optional[str]): The name of the LLM model to use.\n", "        col (str): The name of the column to store the response.\n", "        fanout (bool): Whether to fan out the response as a list.\n", "        overwrite (bool): Whether to overwrite existing data in the response column.\n", "    \"\"\"\n", "\n", "    pt: str  # prompt template\n", "    mapping: dict[str, str] | None = None  # prompt keyword mapping\n", "    temperature: float | None = None  # model temperature\n", "    max_tokens: int | None = None  # model max_tokens\n", "    model: str | None = None  # model name\n", "    col: str = \"response\"  # response column name\n", "    fanout: bool = False  # fanout response column as a list TODO: ensure all cols's new rows are dropped down. TODO: how to un-fan?\n", "    unfan_col: str | None = None  # if fanout, specify the column to un-fan #TODO - THIS IS NOT FUNCTIONAL, SEE NOTE BELOW\n", "    overwrite: bool = False  # overwrite existing response column\n", "\n", "\n", "class ParallelLLMDataFrameProcessor:\n", "    \"\"\"Process dataframe rows in parallel with multiple LLM chains.\"\"\"\n", "\n", "    def __init__(\n", "        self,\n", "        def_model: str,\n", "        def_temperature: float,\n", "        def_max_tokens: int,\n", "        def_async_rate_limit: int,\n", "        def_thread_rate_limit: int,\n", "    ):\n", "        \"\"\"\n", "        Initialize the ParallelLLMDataFrameProcessor.\n", "        Dataframe columns are used to fill in prompt keywords in the prompt templates.\n", "        Dataframe rows are processed in parallel.\n", "\n", "        Args:\n", "            def_model (str): Default model name.\n", "            def_temperature (float): Default model temperature.\n", "            def_max_tokens (int): Default model max_tokens.\n", "            def_async_rate_limit (int): Default async rate limit (litellm models).\n", "            def_thread_rate_limit (int): Default thread rate limit (X.ai Grok models).\n", "        \"\"\"\n", "        self.def_model = def_model\n", "        self.def_temperature = def_temperature\n", "        self.def_max_tokens = def_max_tokens\n", "        self.def_async_rate_limit = def_async_rate_limit\n", "        self.def_thread_rate_limit = def_thread_rate_limit\n", "        self.throttler = Throttler(\n", "            rate_limit=def_async_rate_limit, period=1.0, retry_interval=0.1\n", "        )\n", "        self.thread_pool = ThreadPoolExecutor(max_workers=def_thread_rate_limit)\n", "        logger.debug(\n", "            f\"Initialized with def_model={def_model}, def_temperature={def_temperature}, def_max_tokens={def_max_tokens}, rate_limit={def_async_rate_limit}\"\n", "        )\n", "\n", "    @staticmethod\n", "    def extract_fstring_keywords(fstring: str) -> list[str]:\n", "        \"\"\"\n", "        Extract keywords from an f-string.\n", "        Args: fstring (str): The f-string to extract keywords from.\n", "\n", "        Returns: List[str]: A list of extracted keywords.\n", "        \"\"\"\n", "        formatter = string.Formatter()\n", "        keywords = [\n", "            field_name for _, field_name, _, _ in formatter.parse(fstring) if field_name\n", "        ]\n", "        return keywords\n", "\n", "    @staticmethod\n", "    async def _fanout_list(text: str) -> list[str]:\n", "        \"\"\"\n", "        Convert a comma-separated string into a list. Assumes LLM output is a list.\n", "        TODO: unclear if it handles commas within the list items.\n", "        Args: text (str): The comma-separated string.\n", "\n", "        Returns: List[str]: The list of items.\n", "        \"\"\"\n", "        text = text.strip()\n", "        if not ((\"[\" in text) and (\"]\" in text)):\n", "            raise ValueError(f\"Response '{text[:10]}...{text[-10:]}' is not a list\")\n", "        text = text.split(\"[\")[1].split(\"]\")[0].strip()\n", "        text = text.replace(\"\\n\\n\", \"\\n\").replace(\"\\n\", \",\").replace(\";\", \",\")\n", "        text = text.split(\",\")\n", "        chunks = [x.strip(\"'\\\" \") for x in text if x]\n", "        return chunks\n", "\n", "    async def _async_get_response(\n", "        self,\n", "        message: str,\n", "        temperature: float,\n", "        max_tokens: int,\n", "        model: str,\n", "        returns_list: bool = False,\n", "        verbose: bool = False,\n", "    ) -> str | list[str]:\n", "        \"\"\"\n", "        Get a response from the LLM model asynchronously.\n", "\n", "        Args:\n", "            message (str): The prompt message.\n", "            temperature (float): The temperature for the LLM model.\n", "            max_tokens (int): The maximum number of tokens for the LLM response.\n", "            model (str): The name of the LLM model.\n", "            returns_list (bool): Whether the response should be a list.\n", "\n", "        Returns:\n", "            Union[str, List[str]]: The LLM response.\n", "        \"\"\"\n", "        async with self.throttler:\n", "            start = time.time()\n", "            try:\n", "                messages = [\n", "                    {\"role\": \"system\", \"content\": \"You are a helpful assistant.\"},\n", "                    {\"role\": \"user\", \"content\": message},\n", "                ]\n", "                resp = await litellm.acompletion(\n", "                    model=model,\n", "                    messages=messages,\n", "                    temperature=temperature,\n", "                    max_tokens=max_tokens,\n", "                )\n", "                text = resp[\"choices\"][0][\"message\"][\"content\"]\n", "            except Exception as e:\n", "                error_type = type(e).__name__\n", "                error_message = str(e)\n", "                logger.error(\n", "                    f\"Failed API call model={model}: {error_type} - {error_message}\"\n", "                )\n", "                if verbose:\n", "                    logger.error(\n", "                        f\"Full traceback: {traceback.format_exc()}\"\n", "                    )  # NOTE - THIS GENERATE A LOT OF PRINT OUT INFO TODO, make conditional\n", "                raise\n", "            else:\n", "                if returns_list:\n", "                    text_list = await self._fanout_list(text)\n", "                    logger.info(\n", "                        f\"Prompt: ['{text_list[0]}',...,'{text_list[-1]}'] len={len(text_list)} ✅ in {time.time() - start:.0f}s using {model}\"\n", "                    )\n", "                    return text_list\n", "                logger.info(\n", "                    f\"Prompt: '{text[:10]}...{text[-10:]}' ✅ in {time.time() - start:.0f}s using {model}\"\n", "                )\n", "                return text\n", "\n", "    async def _process_chain_step(\n", "        self,\n", "        c_row: pd.Series,\n", "        step: ChainStep,\n", "        mapping: dict,\n", "        max_attempts: int,\n", "    ) -> None:\n", "        \"\"\"\n", "        Process a single chain step for a given row.\n", "\n", "        Args:\n", "            c_row (pd.Series): The current row of the DataFrame.\n", "            step (ChainStep): The chain step to process.\n", "            mapping (dict): The mapping of prompt keywords to column names.\n", "            max_attempts (int): The maximum number of retry attempts for failed API calls.\n", "        \"\"\"\n", "        prompt = c_row.get(step.pt)\n", "        if c_row.get(step.col) and not step.overwrite:\n", "            logger.debug(f\"{step.col} already exists (skip)\")\n", "            return\n", "        for k, v in mapping.items():\n", "            prompt = prompt.replace(f\"{{{k}}}\", str(c_row.get(v, \"N/A\")))\n", "\n", "        temperature = (\n", "            c_row.get(\"__temperature\") or step.temperature or self.def_temperature\n", "        )\n", "        max_tokens = c_row.get(\"__max_tokens\") or step.max_tokens or self.def_max_tokens\n", "        model = c_row.get(\"__model\") or step.model or self.def_model\n", "\n", "        try:\n", "            if max_attempts > 1:\n", "                foo = tenacity.retry(\n", "                    stop=tenacity.stop_after_attempt(max_attempts),\n", "                    wait=tenacity.wait_exponential(exp_base=2, multiplier=2),\n", "                    after=tenacity.after_log(logger=logger, log_level=1),\n", "                )(self._async_get_response)\n", "                response = await foo(\n", "                    message=prompt,\n", "                    temperature=temperature,\n", "                    max_tokens=max_tokens,\n", "                    model=model,\n", "                    returns_list=step.fanout,\n", "                )\n", "            else:\n", "                response = await self._async_get_response(\n", "                    message=prompt,\n", "                    temperature=temperature,\n", "                    max_tokens=max_tokens,\n", "                    model=model,\n", "                    returns_list=step.fanout,\n", "                )\n", "                # logger.info(f\"Response: {response}\")\n", "        except Exception as e:\n", "            error_msg = str(e).replace(\"\\n\", \" \")\n", "            logger.error(f\"Error in _process_chain_step: {error_msg}\")\n", "            c_row[step.col] = f\"[N/A] [ERROR: {error_msg}]\"\n", "        else:\n", "            c_row[step.col] = response\n", "\n", "    async def _process_chain(\n", "        self,\n", "        input_row: pd.Series,\n", "        chain: list[ChainStep],\n", "        max_attempts: int,\n", "    ) -> list[pd.Series]:\n", "        \"\"\"\n", "        Process a chain of prompt steps for a given row.\n", "\n", "        Args:\n", "            input_row (pd.Series): The input row of the DataFrame.\n", "            chain (List[ChainStep]): The list of chain steps to process.\n", "            max_attempts (int): The maximum number of retry attempts for failed API calls.\n", "            generate_variable_values (bool): Whether to generate variable values.\n", "\n", "        Returns:\n", "            List[pd.Series]: The list of processed rows.\n", "        \"\"\"\n", "        assert max_attempts >= 1\n", "        c_rows = [input_row.copy()]\n", "\n", "        for step in chain:\n", "            if step.mapping is None:\n", "                keywords = self.extract_fstring_keywords(step.pt)\n", "                mapping = {k: k for k in keywords}\n", "            else:\n", "                mapping = step.mapping\n", "\n", "            tasks = [\n", "                self._process_chain_step(c_row, step, mapping, max_attempts)\n", "                for c_row in c_rows\n", "            ]\n", "            await asyncio.gather(*tasks)\n", "\n", "            if step.fanout:\n", "                df = pd.DataFrame(c_rows)\n", "                df = df.explode(column=step.col)\n", "                c_rows = [r for _, r in df.iterrows()]\n", "        return c_rows\n", "\n", "    def _process_df_with_llm_parallel(\n", "        self,\n", "        df: pd.<PERSON><PERSON><PERSON><PERSON>,\n", "        chain: list[ChainStep],\n", "        n_rows: int | None,\n", "        max_attempts: int,\n", "    ) -> pd.DataFrame:\n", "        \"\"\"\n", "        Process a DataFrame with LLM chains in parallel.\n", "\n", "        Args:\n", "            df (pd.DataFrame): The input DataFrame.\n", "            chain (List[ChainStep]): The list of chain steps to process.\n", "            n_rows (Optional[int]): The number of rows to process (None = all rows).\n", "            max_attempts (int): The maximum number of retry attempts for failed API calls.\n", "            generate_variable_values (bool): Whether to generate variable values.\n", "        Returns:\n", "            pd.DataFrame: The processed DataFrame.\n", "        \"\"\"\n", "        assert max_attempts >= 0\n", "        df_to_process = df.head(n_rows).copy() if n_rows is not None else df.copy()\n", "\n", "        async def process_all_rows():\n", "            tasks = [\n", "                self._process_chain(row, chain, max_attempts)\n", "                for _, row in df_to_process.iterrows()\n", "            ]\n", "            return await asyncio.gather(*tasks)\n", "\n", "        loop = asyncio.get_event_loop()\n", "        processed_rows = loop.run_until_complete(process_all_rows())\n", "        unnested_rows = [x for y in processed_rows for x in y]\n", "        result_df = pd.DataFrame(unnested_rows, index=range(len(unnested_rows)))\n", "\n", "        # Ensure all columns from the original DataFrame are present\n", "        for col in df.columns:\n", "            if col not in result_df.columns:\n", "                result_df[col] = df[col]\n", "        return result_df\n", "\n", "    def execute_chain(\n", "        self,\n", "        df: pd.<PERSON><PERSON><PERSON><PERSON>,\n", "        chain: list[ChainStep],\n", "        max_attempts: int = 1,\n", "        n_rows: int | None = None,\n", "        async_rate_limit: int | None = None,\n", "        thread_rate_limit: int | None = None,\n", "    ) -> pd.DataFrame:\n", "        \"\"\"\n", "        Execute a chain of prompts from a dataframe in parallel.\n", "\n", "        Model parameter precedence:\n", "        1. Dataframe: columns  __model, __temperature, __max_tokens\n", "        2. Chain step parameters: model, temperature, max_tokens\n", "        3. Default dataframe processor parameters: def_model, def_temperature, def_max_tokens\n", "\n", "        Args:\n", "            df (pd.DataFrame): Prompt templates to process.\n", "            chain (list[ChainStep]): A list of ChainStep objects.\n", "            n_rows (Optional[int], optional): Number of head rows to process. Defaults to None.\n", "            max_attempts (int, optional): Number of max attempts per API request. Defaults to 1.\n", "            async_rate_limit (Optional[int], optional): Apply new rate limit for async API calls (litellm models).\n", "            thread_rate_limit (Optional[int], optional): Apply new rate limit for thread API calls (Grok models).\n", "        Returns:\n", "            pd.DataFrame: The processed DataFrame.\n", "        \"\"\"\n", "        self._update_rate_limits(async_rate_limit, thread_rate_limit)\n", "        result_df = self._process_df_with_llm_parallel(\n", "            df,\n", "            chain,\n", "            n_rows,\n", "            max_attempts=max_attempts,\n", "        )\n", "        self._reset_rate_limits()\n", "        return result_df\n", "\n", "    def _update_rate_limits(\n", "        self, async_rate_limit: int | None, thread_rate_limit: int | None\n", "    ):\n", "        if async_rate_limit is not None:\n", "            self.throttler = Throttler(\n", "                rate_limit=async_rate_limit, period=1.0, retry_interval=0.1\n", "            )\n", "        if thread_rate_limit is not None:\n", "            self.thread_pool = ThreadPoolExecutor(max_workers=thread_rate_limit)\n", "\n", "    def _reset_rate_limits(self):\n", "        self.throttler = Throttler(\n", "            rate_limit=self.def_async_rate_limit, period=1.0, retry_interval=0.1\n", "        )\n", "        self.thread_pool = ThreadPoolExecutor(max_workers=self.def_thread_rate_limit)\n", "\n", "    def rerun_chain(\n", "        self,\n", "        df: pd.<PERSON><PERSON><PERSON><PERSON>,\n", "        chain: list[ChainStep],\n", "        n_rows: int | None = None,\n", "        max_attempts: int = 1,\n", "        async_rate_limit: int | None = None,\n", "        thread_rate_limit: int | None = None,\n", "    ) -> pd.DataFrame:\n", "        \"\"\"\n", "        Rerun a chain of prompts from a dataframe in parallel.\n", "\n", "        TODO: Explore merging this functionality with execute_chain method to reduce code duplication\n", "        and simplify the API. Consider adding a 'rerun' bool flag parameter to execute_chain instead.\n", "\n", "        Args:\n", "            df (pd.DataFrame): Prompt templates to process.\n", "            chain (list[ChainStep]): A list of ChainStep objects.\n", "            n_rows (Optional[int], optional): Number of head rows to process. Defaults to None.\n", "            max_attempts (int, optional): Number of max attempts per API request. Defaults to 1.\n", "            async_rate_limit (Optional[int], optional): Apply new rate limit for async API calls (litellm models).\n", "            thread_rate_limit (Optional[int], optional): Apply new rate limit for thread API calls (Grok models).\n", "\n", "        Returns:\n", "            pd.DataFrame: The processed DataFrame.\n", "        \"\"\"\n", "        df = df.copy()\n", "        df[\"id\"] = [uuid4() for _ in range(len(df))]\n", "        response_columns: list[str] = [step.col for step in chain]\n", "        df[response_columns] = df[response_columns].map(\n", "            lambda x: \"\" if x.startswith(\"[N/A]\") else x\n", "        )\n", "        result_df = self.execute_chain(\n", "            df,\n", "            chain,\n", "            n_rows,\n", "            max_attempts=max_attempts,\n", "            async_rate_limit=async_rate_limit,\n", "            thread_rate_limit=thread_rate_limit,\n", "        )\n", "        return result_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## **base.py**"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["\"\"\"Module containing base classes and utilities for LLM response generation and validation.\"\"\"\n", "\n", "from dataclasses import dataclass\n", "\n", "import streamlit as st\n", "\n", "# Temperature values for reruns (0 is for original run)\n", "RERUN_TEMPERATURES = [\n", "    0.0,\n", "    0.1,\n", "    0.2,\n", "    0.3,\n", "    0.4,\n", "    0.5,\n", "    0.6,\n", "    0.7,\n", "    0.8,\n", "    0.9,\n", "    1,\n", "]\n", "\n", "\n", "@dataclass\n", "class ResponseConfig:\n", "    \"\"\"Container for LLM response validation results and metadata.\n", "\n", "    Attributes:\n", "        rerun_count: Number of reruns attempted\n", "        current_temperature: Temperature used for the final response\n", "        response: Generated text content from LLM\n", "    \"\"\"\n", "\n", "    rerun_count: int = 3\n", "    current_temperature: float = 0.1\n", "    response: str = None\n", "\n", "\n", "@dataclass\n", "class RerunConfig:\n", "    \"\"\"Configuration for LLM response validation and rerun logic.\n", "\n", "    Attributes:\n", "        rerun_on_fail: Whether to attempt reruns on validation failure\n", "        max_reruns: Maximum number of rerun attempts\n", "    \"\"\"\n", "\n", "    rerun_on_fail: bool = True\n", "    max_reruns: int = 3\n", "\n", "\n", "class LLMResponseGenerator:\n", "    \"\"\"Main class for generating and validating LLM responses with automatic rerun logic.\n", "\n", "    Attributes:\n", "        config: Rerun configuration settings\n", "    \"\"\"\n", "\n", "    def __init__(self, config: RerunConfig):\n", "        \"\"\"Initialize with given rerun configuration.\"\"\"\n", "        self.config = config\n", "\n", "    def check_server_capacity_and_skip_reruns_past_3_if_busy(self) -> None:\n", "        \"\"\"Monitor server health and adjust rerun behavior based on error rates.\"\"\"\n", "        logger.debug(\"Checking server capacity\")\n", "\n", "        # Access class-level counter for consecutive errors\n", "        if not hasattr(self, \"_consecutive_server_errors\"):\n", "            self._consecutive_server_errors = 0\n", "            self._last_error_time = 0.0\n", "            logger.debug(\"Initialized error tracking counters\")\n", "\n", "        current_time = time.time()\n", "\n", "        # If it's been more than 5 minutes since last error, reset counter\n", "        if current_time - self._last_error_time > 300:  # 5 minutes\n", "            if self._consecutive_server_errors > 0:\n", "                logger.info(\n", "                    \"Resetting error counter after 5 minutes of stability\"\n", "                )\n", "            self._consecutive_server_errors = 0\n", "\n", "        # If we've had 3 or more consecutive errors\n", "        if self._consecutive_server_errors >= 3:\n", "            original_reruns = self.config.max_reruns\n", "            self.config.max_reruns = min(3, self.config.max_reruns)\n", "\n", "            logger.warning(\n", "                f\"Server appears busy ({self._consecutive_server_errors} consecutive errors). Limiting reruns from {original_reruns} to {self.config.max_reruns}\"\n", "            )\n", "\n", "            # Add exponential backoff wait time\n", "            wait_time = min(\n", "                30, 2**self._consecutive_server_errors\n", "            )  # Cap at 30 seconds\n", "            logger.info(\n", "                f\"Waiting {wait_time} seconds before next attempt...\"\n", "            )\n", "            time.sleep(wait_time)\n", "\n", "        logger.debug(\n", "            f\"Server status check - Consecutive errors: {self._consecutive_server_errors}\"\n", "        )\n", "\n", "    def generate(\n", "        self, message: str, model: str, max_tokens: int = 4096\n", "    ) -> ResponseConfig:\n", "        \"\"\"Generate LLM response with automatic retry and temperature scaling logic.\"\"\"\n", "        logger.info(\n", "            f\"Starting LLM generation with model: {model}\"\n", "        )\n", "\n", "        temperature = 0.1\n", "\n", "        for rerun_count in range(self.config.max_reruns):\n", "            try:\n", "                # Check server capacity\n", "                self.check_server_capacity_and_skip_reruns_past_3_if_busy()\n", "\n", "                # Get temperature for this rerun\n", "                temperature = RERUN_TEMPERATURES[rerun_count]\n", "                logger.debug(\n", "                    f\"Attempt {rerun_count + 1} with temperature {temperature}\"\n", "                )\n", "\n", "                messages = [\n", "                    {\"role\": \"system\", \"content\": \"You are a helpful assistant.\"},\n", "                    {\"role\": \"user\", \"content\": message},\n", "                ]\n", "\n", "                # Get new response from model\n", "                logger.debug(\"Sending request to LLM\")\n", "\n", "                if \"claude_3_7_sonnet\" in model:\n", "                    # Temperature needs to be 1.0 for Claude 3.7 Sonnet thinking\n", "                    resp = litellm.completion(\n", "                        model=model,\n", "                        messages=messages,\n", "                        temperature=1.0,\n", "                        max_tokens=max_tokens,\n", "                        thinking={\"type\": \"enabled\", \"budget_tokens\": 1024},\n", "                    )\n", "                else:\n", "                    resp = litellm.completion(\n", "                        model=model,\n", "                        messages=messages,\n", "                        temperature=temperature,\n", "                        max_tokens=max_tokens,\n", "                    )\n", "\n", "                # Reset error counter on successful call\n", "                if self._consecutive_server_errors > 0:\n", "                    logger.info(\n", "                        f\"Successfully recovered after {self._consecutive_server_errors} errors\"\n", "                    )\n", "                self._consecutive_server_errors = 0\n", "\n", "                response = resp[\"choices\"][0][\"message\"][\"content\"]\n", "                logger.info(\n", "                    f\"Successfully generated response on attempt {rerun_count + 1}\"\n", "                )\n", "                return ResponseConfig(\n", "                    response=response,\n", "                    rerun_count=rerun_count,\n", "                    current_temperature=temperature,\n", "                )\n", "\n", "            except litellm.InternalServerError as e:\n", "                # Increment error counter and update time\n", "                self._consecutive_server_errors += 1\n", "                self._last_error_time = time.time()\n", "\n", "                logger.error(\n", "                    f\"Server error during validation on rerun {rerun_count} (consecutive errors: {self._consecutive_server_errors}): {e!s}\"\n", "                )\n", "                continue\n", "\n", "            except Exception as e:\n", "                logger.error(\n", "                    f\"Rerun {rerun_count} failed during validation with error: {e!s}\"\n", "                )\n", "                continue\n", "\n", "        # If all reruns failed, return final failed result\n", "        logger.error(\n", "            f\"All {self.config.max_reruns} generation attempts failed\"\n", "        )\n", "        return ResponseConfig(\n", "            response=f\"Final LLM output failed after {self.config.max_reruns} reruns\",\n", "            rerun_count=self.config.max_reruns,\n", "            current_temperature=temperature,\n", "        )"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["llm_response_generator = LLMResponseGenerator(RerunConfig())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## **LLM Calls**"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "import pandas as pd\n", "\n", "\n", "def generate_advanced_initial_instructions_from_llm(\n", "    initial_instructions,\n", "    classifier_type,\n", "    output_classes,\n", "    input_variables,\n", "    enhanced_initial_instructions_generation_prompt,\n", ") -> tuple[dict, str, str]:\n", "    config = EnhancedInitialInstructionsPromptConfig(\n", "        initial_instructions=initial_instructions,\n", "        classifier_type=classifier_type,\n", "        output_classes=output_classes,\n", "        input_variables=input_variables,\n", "        template=enhanced_initial_instructions_generation_prompt,\n", "    )\n", "\n", "    full_prompt = get_generate_enhanced_initial_instructions_prompt(config)\n", "\n", "    try:\n", "        llm_response = llm_response_generator.generate(\n", "            full_prompt,\n", "            LL<PERSON><PERSON>claude_3_5_sonnet,\n", "        )\n", "        return parse_enhanced_initial_instructions(llm_response.response)\n", "    except Exception:\n", "        raise\n", "\n", "\n", "def generate_master_prompt_from_llm(\n", "    initial_instructions,\n", "    enhanced_initial_instructions,\n", "    input_examples,\n", "    input_variables,\n", "    classifier_type,\n", "    output_classes,\n", "    master_prompt_generation_prompt,\n", ") -> tuple[dict, str, str]:\n", "    \"\"\"Generate the master prompt from the LLM.\"\"\"\n", "    logger.info(\"Starting master prompt generation\")\n", "\n", "    current_values = {\n", "        \"initial_instructions\": initial_instructions,\n", "        \"enhanced_initial_instructions\": enhanced_initial_instructions,\n", "        \"input_examples\": input_examples,\n", "        \"input_variables\": input_variables,\n", "        \"classifier_type\": classifier_type,\n", "        \"output_classes\": output_classes,\n", "        \"master_prompt_generation_prompt\": master_prompt_generation_prompt,\n", "    }\n", "    logger.debug(\"Current configuration values collected\")\n", "\n", "    config = MasterPromptConfig(\n", "        enhanced_initial_instructions=current_values[\"enhanced_initial_instructions\"],\n", "        classifier_type=current_values[\"classifier_type\"],\n", "        output_classes=current_values[\"output_classes\"],\n", "        input_variables=current_values[\"input_variables\"],\n", "        input_examples=current_values[\"input_examples\"],\n", "        template=current_values[\"master_prompt_generation_prompt\"],\n", "    )\n", "\n", "    # Generate and store new prompt\n", "    logger.debug(\"Generating master prompt template\")\n", "    master_prompt_template = get_generate_master_prompt(config)\n", "\n", "    try:\n", "        llm_response = llm_response_generator.generate(\n", "            master_prompt_template,\n", "            LL<PERSON><PERSON>claude_3_5_sonnet,\n", "        )\n", "        logger.info(\"Successfully generated master prompt\")\n", "        parsed_prompt, parsed_cot = parse_master_prompt(llm_response.response)\n", "        return (current_values, parsed_prompt, parsed_cot)\n", "    except Exception as e:\n", "        logger.error(f\"Failed to generate master prompt: {e!s}\")\n", "        raise\n", "\n", "\n", "def generate_enhanced_test_case_generation_logic_from_llm(\n", "    enhanced_initial_instructions,\n", "    classifier_type,\n", "    output_classes,\n", "    input_variables,\n", "    test_case_generation_logic,\n", "    enhanced_test_case_generation_logic_prompt,\n", ") -> str:\n", "    \"\"\"Generate enhanced test case generation logic from the LLM.\"\"\"\n", "    config = EnhancedTestCasesGenerationLogicConfig(\n", "        enhanced_initial_instructions=enhanced_initial_instructions,\n", "        classifier_type=classifier_type,\n", "        output_classes=output_classes,\n", "        input_variables=input_variables,\n", "        test_case_generation_logic=test_case_generation_logic,\n", "        template=enhanced_test_case_generation_logic_prompt,\n", "    )\n", "\n", "    # Generate and store new prompt\n", "    full_prompt = get_enhanced_test_case_generation_logic_prompt(config)\n", "\n", "    try:\n", "        llm_response = llm_response_generator.generate(\n", "            full_prompt,\n", "            LL<PERSON><PERSON>claude_3_5_sonnet,\n", "        )\n", "        return parse_enhanced_initial_instructions(llm_response.response)\n", "    except Exception:\n", "        raise\n", "\n", "\n", "def generate_inspiration_seeds_from_llm(\n", "    initial_instructions,\n", "    input_examples,\n", "    test_case_generation_logic,\n", "    inspiration_seed_generator_prompt_part1,\n", "    inspiration_seed_generator_prompt_part2,\n", "    inspiration_seeds,\n", ") -> list[dict[str, str]]:\n", "    \"\"\"Generate inspiration seeds for test case generation\"\"\"\n", "    logger.info(\"Starting inspiration seeds generation\")\n", "\n", "    inspiration_seeds = \"\\n\".join(inspiration_seeds)\n", "    config = InspirationSeedGeneratorPromptConfig(\n", "        initial_instructions=initial_instructions,\n", "        input_examples=input_examples,\n", "        test_case_generation_logic=test_case_generation_logic,\n", "        inspiration_seeds=inspiration_seeds,\n", "        template=inspiration_seed_generator_prompt_part1,\n", "    )\n", "\n", "    filled_prompt_1 = get_inspiration_seed_generator_prompt(config).strip()\n", "    full_prompt = (\n", "        f\"{filled_prompt_1}\\n\\n{inspiration_seed_generator_prompt_part2}\".strip()\n", "    )\n", "\n", "    try:\n", "        logger.debug(\n", "            \"Generating LLM response for inspiration seeds\"\n", "        )\n", "        llm_response = llm_response_generator.generate(\n", "            full_prompt,\n", "            LL<PERSON><PERSON>claude_3_5_sonnet,\n", "        )\n", "\n", "        if not llm_response.response.strip():\n", "            logger.error(\"Received empty response from LLM\")\n", "            st.error(\"Received empty response from LLM\")\n", "            return []\n", "\n", "        generated_inspiration_seeds_from_llm = llm_response.response.strip()\n", "        parsed_tags = extract_xml_tags(generated_inspiration_seeds_from_llm)\n", "        generated_inspiration_seeds_str = parsed_tags.get(\n", "            \"test_case_inspiration_seeds\", \"\"\n", "        )\n", "\n", "        if generated_inspiration_seeds_str:\n", "            generated_inspiration_seeds = json.loads(generated_inspiration_seeds_str)\n", "            generated_inspiration_seeds_list = []\n", "            for category, subcategories in generated_inspiration_seeds.items():\n", "                for subcategory, values in subcategories.items():\n", "                    for value in values:\n", "                        value = json.dumps(\n", "                            {\n", "                                \"text\": value,\n", "                                \"category\": category,\n", "                                \"subcategory\": subcategory,\n", "                            }\n", "                        )\n", "                        generated_inspiration_seeds_list.append(value)\n", "            logger.info(\"Generated inspiration seeds\")\n", "            return generated_inspiration_seeds_list\n", "        logger.warning(\n", "            \"No inspiration seeds found in LLM response\"\n", "        )\n", "        st.warning(\"No inspiration seeds found in LLM response\")\n", "        return []\n", "\n", "    except Exception as e:\n", "        logger.error(\n", "            f\"Failed to generate inspiration seeds: {e!s}\"\n", "        )\n", "        return []\n", "\n", "\n", "def generate_test_cases_from_llm_parallel(\n", "    inspiration_seeds: list[str],\n", "    input_vars: list[str],\n", "    response_col: str,\n", "    master_prompt,\n", "    input_examples,\n", "    test_case_generator_prompt,\n", ") -> dict[str, list[str]]:\n", "    \"\"\"Generate test cases in parallel using inspiration seeds\"\"\"\n", "    logger.info(\"Starting parallel test case generation\")\n", "\n", "    if len(inspiration_seeds) == 0:\n", "        logger.warning(\"No inspiration seeds provided\")\n", "        st.warning(\"No inspiration seeds found\")\n", "        return {}\n", "\n", "    full_prompt_with_inspiration_seeds = []\n", "    for inspiration_seed in inspiration_seeds:\n", "        try:\n", "            inspiration_seed_dict = json.loads(inspiration_seed)\n", "            inspiration_seed_str = f\"Text: {inspiration_seed_dict['text']}\\n Category: {inspiration_seed_dict['category']}\\n Subcategory: {inspiration_seed_dict['subcategory']}\"\n", "            config = TestCaseGeneratorPromptConfig(\n", "                master_prompt=master_prompt,\n", "                input_examples=input_examples,\n", "                inspiration_seed=inspiration_seed_str,\n", "                input_vars=\"\\n\".join(list(map(lambda x: x.strip(\"{{}}\"), input_vars))),\n", "                template=test_case_generator_prompt,\n", "            )\n", "        except json.JSONDecodeError:\n", "            config = TestCaseGeneratorPromptConfig(\n", "                master_prompt=master_prompt,\n", "                input_examples=input_examples,\n", "                inspiration_seed=inspiration_seed,\n", "                input_vars=\"\\n\".join(list(map(lambda x: x.strip(\"{{}}\"), input_vars))),\n", "                template=test_case_generator_prompt,\n", "            )\n", "\n", "        full_prompt = get_test_case_generator_prompt(config).strip()\n", "        full_prompt_with_inspiration_seeds.append(full_prompt)\n", "\n", "    df = pd.DataFrame({\"prompt\": full_prompt_with_inspiration_seeds})\n", "    prompt_cols = [col for col in df.columns if \"prompt\" in col.lower()]\n", "\n", "    if len(prompt_cols) != 1:\n", "        logger.error(\n", "            f\"Invalid number of prompt columns: {len(prompt_cols)}\"\n", "        )\n", "        msg = \"Exactly one prompt column is required\"\n", "        raise ValueError(msg)\n", "\n", "    df = preprocess_input(df)\n", "    logger.debug(\n", "        f\"Preprocessed input with prompt column: {prompt_cols[0]}\"\n", "    )\n", "\n", "    llm_proc = ParallelLLMDataFrameProcessor(\n", "        def_model=LLM.claude_3_5_sonnet,\n", "        def_temperature=0.0,\n", "        def_max_tokens=4096,\n", "        def_async_rate_limit=10,\n", "        def_thread_rate_limit=5,\n", "    )\n", "    chain = [\n", "        ChainStep(\n", "            pt=prompt_cols[0],\n", "            col=response_col,\n", "        ),\n", "    ]\n", "\n", "    try:\n", "        start_time = time.time()\n", "        logger.debug(\"Starting parallel LLM processing\")\n", "        result_df = llm_proc.execute_chain(df, chain, max_attempts=3)\n", "        result_df = result_df.drop(columns=[\"prompt\"])\n", "\n", "        # Parse XML tags from responses\n", "        parsed_responses = result_df[response_col].apply(extract_xml_tags)\n", "        parsed_df = pd.json_normalize(parsed_responses)\n", "        result_df = pd.concat([result_df, parsed_df], axis=1)\n", "        result_df = result_df.fillna(\"\")\n", "\n", "        processing_time = time.time() - start_time\n", "        logger.info(\n", "            f\"Completed test case generation in {processing_time:.2f} seconds\"\n", "        )\n", "        return result_df.to_dict(orient=\"records\")\n", "\n", "    except Exception as e:\n", "        logger.error(f\"Failed to generate test cases: {e!s}\")\n", "        return []\n", "\n", "\n", "def generate_master_prompt_classification_output_parallel(\n", "    master_prompt,\n", "    generated_test_cases,\n", "    response_col: str,\n", ") -> pd.DataFrame:\n", "    \"\"\"Generate responses from the LLM in parallel\"\"\"\n", "    logger.info(\n", "        \"Starting parallel master prompt template generation\"\n", "    )\n", "\n", "    df = pd.DataFrame()\n", "    df[\"prompt\"] = get_compiled_master_prompt_template(\n", "        master_prompt, generated_test_cases\n", "    )\n", "    prompt_cols = [col for col in df.columns if \"prompt\" in col.lower()]\n", "    if len(prompt_cols) != 1:\n", "        logger.error(\n", "            f\"Invalid number of prompt columns: {len(prompt_cols)}\"\n", "        )\n", "        msg = \"Exactly one prompt column is required\"\n", "        raise ValueError(msg)\n", "\n", "    logger.debug(f\"Using prompt column: {prompt_cols[0]}\")\n", "    df = preprocess_input(df)\n", "\n", "    llm_proc = ParallelLLMDataFrameProcessor(\n", "        def_model=LLM.claude_3_5_sonnet,\n", "        def_temperature=0.0,\n", "        def_max_tokens=4096,\n", "        def_async_rate_limit=10,\n", "        def_thread_rate_limit=5,\n", "    )\n", "    chain = [\n", "        ChainStep(\n", "            pt=prompt_cols[0],\n", "            col=response_col,\n", "        ),\n", "    ]\n", "\n", "    try:\n", "        start_time = time.time()\n", "        logger.debug(\n", "            f\"Starting parallel LLM processing for {len(df)} prompts\"\n", "        )\n", "        result_df = llm_proc.execute_chain(df, chain, max_attempts=3)\n", "\n", "        # Parse XML tags from responses\n", "        parsed_responses = result_df[response_col].apply(extract_xml_tags, upper=True)\n", "        parsed_df = pd.json_normalize(parsed_responses)\n", "        result_df = pd.concat([result_df, parsed_df], axis=1)\n", "        result_df = result_df.fillna(\"\")\n", "\n", "        processing_time = time.time() - start_time\n", "        logger.info(\n", "            f\"Completed template generation in {processing_time:.2f} seconds\"\n", "        )\n", "        return result_df\n", "\n", "    except Exception as e:\n", "        logger.error(\n", "            f\"Failed to generate template outputs: {e!s}\"\n", "        )\n", "        return pd.DataFrame()\n", "\n", "\n", "def generate_master_prompt_validation_output_parallel(\n", "    compiled_master_prompts: list[str],\n", "    generated_test_cases,\n", "    test_case_validator_prompt,\n", "    response_col: str,\n", ") -> pd.DataFrame:\n", "    \"\"\"Generate responses from the LLM in parallel\"\"\"\n", "    logger.info(\n", "        \"Starting parallel validation output generation\"\n", "    )\n", "\n", "    assert (\n", "        len(compiled_master_prompts)\n", "        == len(generated_test_cases[\"inspiration_seeds\"])\n", "        == len(generated_test_cases[\"<classification>\"])\n", "    ), \"All lists must have the same length\"\n", "\n", "    full_prompts = []\n", "    for compiled_master_prompt, inspiration_seed, classification in zip(\n", "        compiled_master_prompts,\n", "        generated_test_cases[\"inspiration_seeds\"],\n", "        generated_test_cases[\"<classification>\"],\n", "        strict=False,\n", "    ):\n", "        try:\n", "            inspiration_seed_dict = json.loads(inspiration_seed)\n", "            inspiration_seed_str = f\"Text: {inspiration_seed_dict['text']}\\n Category: {inspiration_seed_dict['category']}\\n Subcategory: {inspiration_seed_dict['subcategory']}\"\n", "            config = TestCaseValidatorPromptConfig(\n", "                compiled_master_prompt=compiled_master_prompt,\n", "                inspiration_seed=inspiration_seed_str,\n", "                test_case_output=classification,\n", "                template=test_case_validator_prompt,\n", "            )\n", "        except json.JSONDecodeError:\n", "            config = TestCaseValidatorPromptConfig(\n", "                compiled_master_prompt=compiled_master_prompt,\n", "                inspiration_seed=inspiration_seed,\n", "                test_case_output=classification,\n", "                template=test_case_validator_prompt,\n", "            )\n", "\n", "        full_prompt = get_test_case_validator_prompt(config).strip()\n", "        full_prompts.append(full_prompt)\n", "\n", "    df = pd.DataFrame({\"prompt\": full_prompts})\n", "    prompt_cols = [col for col in df.columns if \"prompt\" in col.lower()]\n", "    if len(prompt_cols) != 1:\n", "        logger.error(\n", "            f\"Invalid number of prompt columns: {len(prompt_cols)}\"\n", "        )\n", "        msg = \"Exactly one prompt column is required\"\n", "        raise ValueError(msg)\n", "\n", "    logger.debug(f\"Using prompt column: {prompt_cols[0]}\")\n", "    df = preprocess_input(df)\n", "\n", "    llm_proc = ParallelLLMDataFrameProcessor(\n", "        def_model=LLM.claude_3_5_sonnet,\n", "        def_temperature=0.0,\n", "        def_max_tokens=4096,\n", "        def_async_rate_limit=10,\n", "        def_thread_rate_limit=5,\n", "    )\n", "    chain = [\n", "        ChainStep(\n", "            pt=prompt_cols[0],\n", "            col=response_col,\n", "        ),\n", "    ]\n", "\n", "    try:\n", "        start_time = time.time()\n", "        logger.debug(\n", "            f\"Starting parallel LLM processing for {len(df)} prompts\"\n", "        )\n", "        result_df = llm_proc.execute_chain(df, chain, max_attempts=3)\n", "\n", "        # Parse XML tags from responses\n", "        parsed_responses = result_df[response_col].apply(extract_xml_tags, upper=True)\n", "        parsed_df = pd.json_normalize(parsed_responses)\n", "        result_df = pd.concat([result_df, parsed_df], axis=1)\n", "        result_df = result_df.fillna(\"\")\n", "\n", "        processing_time = time.time() - start_time\n", "        logger.info(\n", "            f\"Completed template generation in {processing_time:.2f} seconds\"\n", "        )\n", "        return result_df\n", "\n", "    except Exception as e:\n", "        logger.error(\n", "            f\"Failed to generate template outputs: {e!s}\"\n", "        )\n", "        return pd.DataFrame()\n", "\n", "\n", "def generate_master_prompt_template_output_parallel(\n", "    df: pd.<PERSON><PERSON><PERSON><PERSON>,\n", "    response_col: str,\n", ") -> pd.DataFrame:\n", "    \"\"\"Generate responses from the LLM in parallel\"\"\"\n", "    logger.info(\n", "        \"Starting parallel master prompt template generation\"\n", "    )\n", "\n", "    prompt_cols = [col for col in df.columns if \"prompt\" in col.lower()]\n", "    if len(prompt_cols) != 1:\n", "        logger.error(\n", "            f\"Invalid number of prompt columns: {len(prompt_cols)}\"\n", "        )\n", "        msg = \"Exactly one prompt column is required\"\n", "        raise ValueError(msg)\n", "\n", "    logger.debug(f\"Using prompt column: {prompt_cols[0]}\")\n", "    df = preprocess_input(df)\n", "\n", "    llm_proc = ParallelLLMDataFrameProcessor(\n", "        def_model=LLM.claude_3_5_sonnet,\n", "        def_temperature=0.0,\n", "        def_max_tokens=4096,\n", "        def_async_rate_limit=10,\n", "        def_thread_rate_limit=5,\n", "    )\n", "    chain = [\n", "        ChainStep(\n", "            pt=prompt_cols[0],\n", "            col=response_col,\n", "        ),\n", "    ]\n", "\n", "    try:\n", "        start_time = time.time()\n", "        logger.debug(\n", "            f\"Starting parallel LLM processing for {len(df)} prompts\"\n", "        )\n", "        result_df = llm_proc.execute_chain(df, chain, max_attempts=3)\n", "\n", "        # Parse XML tags from responses\n", "        parsed_responses = result_df[response_col].apply(extract_xml_tags, upper=True)\n", "        parsed_df = pd.json_normalize(parsed_responses)\n", "        result_df = pd.concat([result_df, parsed_df], axis=1)\n", "        result_df = result_df.fillna(\"\")\n", "\n", "        processing_time = time.time() - start_time\n", "        logger.info(\n", "            f\"Completed template generation in {processing_time:.2f} seconds\"\n", "        )\n", "        return result_df\n", "\n", "    except Exception as e:\n", "        logger.error(\n", "            f\"Failed to generate template outputs: {e!s}\"\n", "        )\n", "        return pd.DataFrame()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## **Data Interface**"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["from abc import ABC, abstractmethod\n", "from typing import Any\n", "\n", "\n", "class SessionInterface(ABC):\n", "    \"\"\"\n", "    Abstract base class defining the interface for session storage implementations.\n", "    All session storage classes (local, postgres, gsheet) should implement this interface.\n", "    \"\"\"\n", "\n", "    @abstractmethod\n", "    def init_storage(self) -> None:\n", "        \"\"\"Initialize the storage backend (create tables, directories, etc.)\"\"\"\n", "\n", "    @abstractmethod\n", "    def save_test_case_session(self) -> None:\n", "        \"\"\"Save test case generator session state\"\"\"\n", "\n", "    @abstractmethod\n", "    def load_test_case_session(self) -> Any | None:\n", "        \"\"\"Load test case generator session state\"\"\"\n", "\n", "    @abstractmethod\n", "    def save_start_page_session(self) -> None:\n", "        \"\"\"Save start page session state\"\"\"\n", "\n", "    @abstractmethod\n", "    def load_start_page_session(self) -> Any:\n", "        \"\"\"Load start page session state\"\"\"\n", "\n", "    @abstractmethod\n", "    def save_iteration_session(self) -> None:\n", "        \"\"\"Save iteration session state\"\"\"\n", "\n", "    @abstractmethod\n", "    def load_iteration_session(self) -> Any:\n", "        \"\"\"Load iteration session state\"\"\"\n", "\n", "    @abstractmethod\n", "    def export_all_data(self) -> None:\n", "        \"\"\"Export all session data\"\"\"\n", "\n", "    @abstractmethod\n", "    def list_projects(self) -> list[tuple[str, str, str]]:\n", "        \"\"\"List all projects\"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## **Instructions**"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["from collections import OrderedDict\n", "\n", "# TODO: put this in config\n", "cot_keywords = [\n", "    \"scratchpad\",\n", "    \"analysis\",\n", "    \"conclusion\",\n", "    \"classification\",\n", "    \"categories\",\n", "    \"category/categories\",\n", "    \"justification\",\n", "]\n", "\n", "cot_keywords_regex = [\n", "    \"scratchpad\",\n", "    \"analysis\",\n", "    \"classification\",\n", "    \"justification\",\n", "]\n", "\n", "# TODO: move to config\n", "# options for user choice dropdown (config)\n", "classifier_types = OrderedDict(\n", "    {\n", "        \"TRUE / FALSE\": \"TRUE\\nFALSE\",\n", "        \"CATEGORIZER - YOU KNOW THE CATEGORIES\": \"Enter your categories here, replace this line\",\n", "        \"CATEGORIZER - SUGGEST ME CATEGORIES\": \"LLM will suggest categories\",\n", "        \"TAGGER - can have more than one tag per input\": \"TBD - not built yet\",\n", "        \"DECISION ENGINE - [TBD, Future]\": \"^^pick an option above\",\n", "        \"SCORER - edit the scores / add definitions to each score below.TBD See Tom scorer\": \"^^pick an option above\",\n", "    }\n", ")\n", "\n", "# helper UI instructions to guide user through the process\n", "# TODO: add to config / in-line.\n", "initial_prompt_instructions = \"\"\"\n", "    1. User main initial input. \n", "    2. Describe what your classifier should do.\n", "    3. Incase MASTER PROMPT is weird, add additional instructions here.\n", "    4. If you want to generate dummy test cases, add the variable names in {{VARIABLE_NAME}} tags.\n", "    \"\"\"\n", "\n", "input_examples_instructions = \"\"\"\n", "    1. Helps with test_case generation if it's getting it way off format.\n", "    2. Put a single example of the text that will go into the model.\n", "    3. If multi-turn/multiple inputs, concatenate them into a single string. \n", "    4. Use \"---------TITLE---------\"  dividers to separate them.\n", "    5. If you want to give several examples, that's also OK.\n", "        Just make sure to add dividers between them.\n", "\"\"\"\n", "\n", "input_variables_instructions = \"\"\"\n", "    [Use only if strict requirements]\n", "    1. Default text is \"\"Please suggest input variables to me\"\"\n", "    2. a-z characters, numbers and underscores ( _ ) only.\n", "\"\"\"\n", "\n", "master_prompt_instructions = \"\"\"\n", "    Instructions:\n", "    1. Edit MASTER PROMPT yourself if you like.\n", "    2. Generally, try not to. It's doing a lot of work. But you absolutely can.\n", "    3. Delete MASTER PROMPT to return to generated value.\n", "    4. Change instructions above to re-generate MASTER PROMPT\n", "\"\"\"\n", "\n", "test_case_generator_instructions = \"\"\"\n", "    ### How to use the Test Case Generator:\n", "    1. Set the number of test cases you want to generate\n", "    2. Review and modify the prompt templates if needed\n", "    3. Click 'Generate Test Cases' to create your test suite\n", "    4. Review the generated cases in the table\n", "    5. Use the copy button to copy all test cases to your clipboard\n", "\n", "    ### Tips:\n", "    - Generate a variety of test cases by adjusting the prompts\n", "    - Use the data editor to manually modify generated test cases\n", "    - Save your customized prompts for future use\n", "\"\"\"\n", "\n", "test_case_generation_logic_options = OrderedDict(\n", "    {\n", "        \"Positive Instances\": \"Clear examples that should trigger successful/expected responses\",  # Normal operation validation\n", "        \"Negative Instances\": \"Inputs that should be rejected/flagged by the system\",  # Boundary violation checks\n", "        \"Nuanced Edge Cases\": \"Ambiguous cases testing decision boundaries\",  # Complex scenario validation\n", "    }\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## **Utils**"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["\"\"\"Utility functions for prompt processing and content extraction.\"\"\"\n", "\n", "import re\n", "\n", "import pandas as pd\n", "\n", "\n", "def extract_variables(text: str) -> list:\n", "    \"\"\"Extract template variables from text in {{variable}} format.\n", "\n", "    Args:\n", "        text: Input string containing potential variables\n", "\n", "    Returns:\n", "        List of unique variable names found\n", "\n", "    Example:\n", "        >>> extract_variables(\"Hello {{name}}! Today is {{day}}.\")\n", "        ['{{name}}', '{{day}}']\n", "    \"\"\"\n", "    # Extract variables in {{variable_name}} format\n", "    pattern = r\"\\{\\{([^}]+)\\}\\}\"\n", "    matches = re.findall(pattern, text)\n", "    # validation check\n", "    valid_matches = list(filter(lambda x: re.sub(r\"\\W+\", \"\", x).isupper(), matches))\n", "    valid_matches_formatted = list(set(map(lambda x: \"{{\" + x + \"}}\", valid_matches)))\n", "    return valid_matches_formatted\n", "\n", "\n", "def extract_cot_tags(prompt: str, cot_keywords: list[str]) -> list[str]:\n", "    \"\"\"Extract Chain of Thought (COT) related tags from prompt.\n", "\n", "    Args:\n", "        prompt: Classification prompt text\n", "\n", "    Returns:\n", "        List of unique COT-related tags in {{tag}} format\n", "    \"\"\"\n", "    # Find all <tag> patterns\n", "    pattern = r\"<([^>]+)>\"\n", "    matches = re.findall(pattern, prompt)\n", "    # Filter for COT-related tags\n", "    cot_tags = set([match.strip(\"/\").lower() for match in matches])\n", "    return [f\"<{tag}>\" for tag in cot_tags if tag in cot_keywords]\n", "\n", "\n", "def extract_xml_tags(text: str, upper: bool = False) -> dict[str, str]:\n", "    \"\"\"Parse XML-style tags and their content from text.\n", "\n", "    Args:\n", "        text: Input string containing XML-style tags\n", "\n", "    Returns:\n", "        Dictionary mapping tag names to their content\n", "\n", "    Example:\n", "        >>> extract_xml_tags(\"<tag>content</tag>\")\n", "        {'tag': 'content'}\n", "    \"\"\"\n", "    # Pattern to match content between tags, including multiline content\n", "    pattern = r\"<(\\w+)>\\s*(.*?)\\s*</\\1>\"\n", "\n", "    # re.DOTALL flag allows . to match newlines\n", "    matches = re.finditer(pattern, text, re.DOTALL)\n", "\n", "    # Create dictionary of tag_name: content pairs\n", "    tag_contents = {}\n", "    for match in matches:\n", "        tag_name = match.group(1)\n", "        content = match.group(2).strip()\n", "        # Handle case-insensitive duplicate columns by merging into uppercase versions\n", "        if upper:\n", "            tag_contents[tag_name.upper()] = content\n", "        else:\n", "            tag_contents[tag_name] = content\n", "\n", "    return tag_contents\n", "\n", "\n", "def parse_master_prompt(response: str) -> tuple[str, str]:\n", "    \"\"\"Parse the master prompt into a tuple of (master_prompt, llm_scratchpad).\n", "\n", "    Args:\n", "        response: The response from the LLM\n", "\n", "    Returns:\n", "        A tuple of (master_prompt, llm_scratchpad)\n", "\n", "    Example:\n", "        >>> parse_master_prompt(\"{{master_prompt}}\")\n", "        ('{{master_prompt}}', '')\n", "    \"\"\"\n", "    # Extract master prompt from response\n", "    llm_scratchpad, master_prompt = response.split(\n", "        \"-----------------END OF SCRATCHPAD----------------------\"\n", "    )\n", "    master_prompt = (\n", "        master_prompt.split(\"</CLASSIFICATION_PROMPT_OUTPUT>\")[0]\n", "        .split(\"<CLASSIFICATION_PROMPT_OUTPUT>\")[-1]\n", "        .strip()\n", "    )\n", "    llm_scratchpad = (\n", "        llm_scratchpad.split(\"</SCRATCHPAD>\")[0].split(\"<SCRATCHPAD>\")[-1].strip()\n", "    )\n", "    return master_prompt, llm_scratchpad\n", "\n", "\n", "def parse_enhanced_initial_instructions(response: str) -> str:\n", "    \"\"\"Parse the enhanced initial instructions into a tuple of (enhanced_initial_instructions, llm_scratchpad).\n", "\n", "    Args:\n", "        response: The response from the LLM\n", "\n", "    Returns:\n", "        enhanced_initial_instructions\n", "\n", "    Example:\n", "        >>> parse_enhanced_initial_instructions(\"{{enhanced_initial_instructions}}\")\n", "        ('{{enhanced_initial_instructions}}', '')\n", "    \"\"\"\n", "    # Remote <think>...</think> section from the response\n", "    return re.sub(r\"<think>.*?</think>\", \"\", response, flags=re.DOTALL).strip()\n", "\n", "\n", "def preprocess_input(df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"\n", "    Preprocess the input DataFrame.\n", "\n", "    This function takes a DataFrame as input and applies preprocessing steps\n", "    to ensure the data is in a consistent and usable format for the LLM processor.\n", "    It handles missing values by filling them with a placeholder string.\n", "\n", "    Args:\n", "        df (pd.DataFrame): The input DataFrame to preprocess.\n", "\n", "    Returns:\n", "        pd.DataFrame: The preprocessed DataFrame with missing values handled.\n", "    \"\"\"\n", "    # Implement preprocessing logic here\n", "    # For example, handling NaN values:\n", "    return df.fillna(\"NA\")\n", "\n", "\n", "# Helper function to fill prompt template\n", "def get_compiled_master_prompt_template(template: str, test_cases: dict) -> list[str]:\n", "    \"\"\"Fill template variables in master prompt\"\"\"\n", "    variables = extract_variables(template)\n", "    templates = []\n", "    for i in range(len(test_cases[\"inspiration_seeds\"])):\n", "        template = template\n", "        for var in variables:\n", "            template = template.replace(var, test_cases[var][i])\n", "        templates.append(template)\n", "    return templates"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## **Test Case Page**"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["\"\"\"Streamlit page for generating and managing test cases for classification systems.\"\"\"\n", "\n", "import uuid\n", "from typing import Any\n", "\n", "import pandas as pd\n", "\n", "\n", "def extract_conclusion_tag(text: str) -> Any:\n", "    \"\"\"More robust extraction that handles attributes and malformed XML.\"\"\"\n", "    pattern = re.compile(\n", "        r\"<conclusion(?:\\s+[^>]*)?>([^<]*(?:<(?!/?conclusion)[^<]*)*)</conclusion>\",\n", "        re.DOTALL | re.IGNORECASE,\n", "    )\n", "    matches = pattern.search(text)\n", "    if matches:\n", "        match = matches.group(1).strip()\n", "        if \"true\" in match.lower():\n", "            return \"✅\"\n", "        if \"false\" in match.lower():\n", "            return \"❌\"\n", "    return \"❓\"\n", "\n", "\n", "def get_test_case_generator(\n", "    enhanced_initial_instructions,\n", "    classifier_type,\n", "    output_classes,\n", "    input_variables,\n", "    master_prompt,\n", "    test_case_generation_logic,\n", "    inspiration_seeds: list[str],\n", "    run_classification: bool = False,\n", ") -> None:\n", "    \"\"\"Main function for rendering the test case generator page.\n", "\n", "    Handles:\n", "    - User input for test case parameters\n", "    - Prompt template management\n", "    - LLM interaction for test case generation\n", "    - Result display and export functionality\n", "    \"\"\"\n", "    logger.info(\"Loading test case generator page\")\n", "    input_vars = extract_variables(master_prompt)\n", "    if not input_vars:\n", "        logger.error(\"No variables extracted from master prompt\")\n", "        return None\n", "\n", "    # Generate enhanced test case generation logic\n", "    enhanced_test_case_generation_logic = (\n", "        generate_enhanced_test_case_generation_logic_from_llm(\n", "            enhanced_initial_instructions,\n", "            classifier_type,\n", "            output_classes,\n", "            input_variables,\n", "            test_case_generation_logic,\n", "            ENHANCED_TEST_CASE_GENERATION_LOGIC_PROMPT_TEMPLATE,\n", "        )\n", "    )\n", "    logger.info(\"Enhanced test case generation logic generated successfully\")\n", "\n", "    input_vars_master_prompt = extract_variables(master_prompt)\n", "    logger.debug(\"Generating inspiration seeds\")\n", "    generated_inspiration_seeds = generate_inspiration_seeds_from_llm(\n", "        initial_instructions,\n", "        input_examples,\n", "        test_case_generation_logic,\n", "        INSPIRATION_SEED_GENERATOR_PROMPT1,\n", "        INSPIRATION_SEED_GENERATOR_PROMPT2,\n", "        inspiration_seeds,\n", "    )\n", "    if len(generated_inspiration_seeds) > 0:\n", "        logger.info(f\"Generated {len(generated_inspiration_seeds)} inspiration seeds\")\n", "        inspiration_seeds = inspiration_seeds + generated_inspiration_seeds\n", "        test_case_type = [enhanced_test_case_generation_logic] * len(\n", "            generated_inspiration_seeds\n", "        )\n", "\n", "        logger.debug(\"Generating test cases from inspiration seeds\")\n", "        input_vars_master_prompt = extract_variables(master_prompt)\n", "        generated_test_cases = generate_test_cases_from_llm_parallel(\n", "            inspiration_seeds=generated_inspiration_seeds,\n", "            input_vars=input_vars_master_prompt,\n", "            response_col=\"response\",\n", "            master_prompt=master_prompt,\n", "            input_examples=input_examples,\n", "            test_case_generator_prompt=TEST_CASE_GENERATOR_PROMPT,\n", "        )\n", "\n", "        if len(generated_test_cases) > 0:\n", "            logger.info(f\"Generated {len(generated_test_cases)} test cases\")\n", "            generated_test_cases_dict = {}\n", "            # Add variables to session state\n", "            for test_case in generated_test_cases:\n", "                for var, value in test_case.items():\n", "                    if \"{{\" + var + \"}}\" in input_vars_master_prompt:\n", "                        generated_test_cases_dict[\n", "                            \"{{\" + var + \"}}\"\n", "                        ] = generated_test_cases_dict.get(\"{{\" + var + \"}}\", []) + [\n", "                            value\n", "                        ]\n", "\n", "            ids = [str(uuid.uuid4()) for _ in range(len(generated_test_cases))]\n", "            generated_test_cases_dict[\"ID\"] = (\n", "                generated_test_cases_dict.get(\"ID\", []) + ids\n", "            )\n", "            logger.debug(\"Test cases saved to session\")\n", "        else:\n", "            logger.warning(\"No test cases generated from inspiration seeds\")\n", "    else:\n", "        logger.warning(\"No inspiration seeds generated\")\n", "\n", "    if run_classification:\n", "        logger.info(\"Running classification master prompt\")\n", "        # Add your classification master prompt logic here\n", "        result_df = generate_master_prompt_classification_output_parallel(\n", "            master_prompt,\n", "            generated_test_cases_dict,\n", "            \"response\",\n", "        )\n", "        logger.info(\n", "            \"Classification master prompt result columns: {list(result_df.columns)}\"\n", "        )\n", "        generated_test_cases_dict[\"test_case_llm_response\"] = result_df[\n", "            \"response\"\n", "        ].tolist()\n", "        if \"CLASSIFICATION\" in list(result_df.columns):\n", "            generated_test_cases_dict[\"test_case_classification\"] = result_df[\n", "                \"CLASSIFICATION\"\n", "            ].tolist()\n", "            # Check against expectation\n", "            validation_df = generate_master_prompt_validation_output_parallel(\n", "                result_df[\"prompt\"].tolist(),\n", "                generated_test_cases_dict,\n", "                TEST_CASE_VALIDATOR_PROMPT,\n", "                response_col=\"response\",\n", "            )\n", "\n", "            generated_test_cases_dict[\"test_case_validation\"] = (\n", "                validation_df[\"SCRATCHPAD\"].apply(extract_conclusion_tag).tolist()\n", "            )\n", "\n", "    generated_test_cases_dict[\"inspiration_seeds\"] = inspiration_seeds\n", "    generated_test_cases_dict[\"test_case_type\"] = test_case_type\n", "    return generated_test_cases_dict"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## **Iteration Page**"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["\"\"\"Module for iterative prompt refinement and evaluation.\"\"\"\n", "\n", "import pandas as pd\n", "\n", "\n", "def get_ordered_columns(groups: dict) -> list:\n", "    \"\"\"Get columns in defined group order\"\"\"\n", "    return [\n", "        col\n", "        for group in sorted(groups.values(), key=lambda x: x[\"order\"])\n", "        for col in group[\"columns\"]\n", "    ]\n", "\n", "\n", "# Helper function to fill prompt template\n", "def get_compiled_master_prompt_template(template: str, variables: dict) -> str:\n", "    \"\"\"Fill template variables in master prompt\"\"\"\n", "    for var, value in variables.items():\n", "        # Fix: Ensure value is a string before replacement\n", "        if value is None:\n", "            value = \"\"  # Replace None with empty string  # noqa: PLW2901\n", "        template = template.replace(var, str(value))\n", "    return template\n", "\n", "\n", "def update_compiled_master_prompt_template(compiled_master_prompts: list[str], instructions: str\n", ") -> None:\n", "    \"\"\"Update compiled prompt template with additional instructions\"\"\"\n", "\n", "    def apply_instructions(template: str, instructions: str) -> str:\n", "        # Handle case where the template doesn't have the expected structure\n", "        if \"## ADDITIONAL INSTRUCTIONS\" not in template:\n", "            return template + f\"\\n\\n## ADDITIONAL INSTRUCTIONS\\n{instructions}\\n\"\n", "\n", "        if \"Add additional instructions here.\" not in template:\n", "            before_part = template.split(\"## ADDITIONAL INSTRUCTIONS\")[0].strip()\n", "            return before_part + f\"\\n\\n## ADDITIONAL INSTRUCTIONS\\n{instructions}\\n\"\n", "\n", "        # Normal case with expected structure\n", "        before_part = template.split(\"## ADDITIONAL INSTRUCTIONS\")[0].strip()\n", "        after_part = (\n", "            template.split(\"Add additional instructions here.\")[1].strip()\n", "            if \"Add additional instructions here.\" in template\n", "            else \"\"\n", "        )\n", "        return (\n", "            before_part\n", "            + f\"\\n\\n## ADDITIONAL INSTRUCTIONS\\n{instructions}\\nAdd additional instructions here.\\n\\n\"\n", "            + after_part\n", "        )\n", "    return [apply_instructions(x, instructions) for x in compiled_master_prompts]\n", "\n", "\n", "def get_iteration_page(\n", "        master_prompt: str,\n", "        generated_test_cases: dict[str, list],\n", "        additional_instructions: str\n", ") -> None:\n", "    \"\"\"Page for iterative prompt refinement and evaluation.\n", "\n", "    Features:\n", "    - Side-by-side comparison of test cases and model outputs\n", "    - Scoring system for result quality\n", "    - Interactive feedback mechanism\n", "    - Version tracking for iterations\n", "    \"\"\"\n", "    # Load test cases and prepare data\n", "    test_cases_lengths = [len(v) for v in generated_test_cases.values()]\n", "    if len(test_cases_lengths) > 0:\n", "        logger.info(\"Processing {max(test_cases_lengths)} test cases\")\n", "        max_cases = max(test_cases_lengths)\n", "        non_input_vars = [\"ID\", \"test_case_type\", \"inspiration_seeds\"]\n", "        input_vars_test_cases = [\n", "            var\n", "            for var in list(generated_test_cases.keys())\n", "            if var not in non_input_vars\n", "        ]\n", "        input_vars_master_prompt = extract_variables(master_prompt)\n", "        input_vars = list(set(input_vars_test_cases + input_vars_master_prompt))\n", "\n", "        test_cases_data = {}\n", "        for var in non_input_vars + input_vars:\n", "            cases = generated_test_cases.get(var, [])\n", "            test_cases_data[var] = [\n", "                cases[idx] if idx < len(cases) else \"❌ MISSING\"\n", "                for idx in range(max_cases)\n", "            ]\n", "    else:\n", "        logger.error(\"No valid test cases found\")\n", "        st.error(\"No valid test cases found!\")\n", "        return None\n", "\n", "    # Update the CoT variables extraction to ensure unique names\n", "    cot_vars = extract_cot_tags(master_prompt, cot_keywords)\n", "    logger.debug(\"Extracted CoT variables: {cot_vars}\")\n", "\n", "    # Create DataFrame with all required columns\n", "    iteration_df = pd.DataFrame(\n", "        columns=[  # noqa: RUF005\n", "            \"ID\",\n", "            \"TEST CASE TYPE\",\n", "            \"MASTER PROMPT TEMPLATE\",\n", "            \"COMPILED MASTER PROMPT TEMPLATE\",\n", "            \"LLM RESPONSE\",\n", "            *cot_vars,  # Add CoT columns\n", "            \"SCORE (1-7)\",\n", "            \"NOTES\",\n", "            \"ADDITIONAL INSTRUCTIONS\",\n", "            \"INSPIRATION SEEDS\",\n", "        ]\n", "        + input_vars,\n", "        data=[\n", "            {\n", "                **{var: test_cases_data[var][idx] for var in input_vars},\n", "                \"ID\": test_cases_data[\"ID\"][idx],\n", "                \"TEST CASE TYPE\": test_cases_data[\"test_case_type\"][idx],\n", "                \"MASTER PROMPT TEMPLATE\": master_prompt,\n", "                \"COMPILED MASTER PROMPT TEMPLATE\": get_compiled_master_prompt_template(\n", "                    master_prompt,\n", "                    {\n", "                        var: test_cases_data[var][idx]\n", "                        for var in input_vars_master_prompt\n", "                    },\n", "                ),\n", "                \"LLM RESPONSE\": \"\",\n", "                **dict.fromkeys(cot_vars, \"\"),  # Initialize CoT columns\n", "                \"SCORE (1-7)\": None,\n", "                \"NOTES\": \"\",\n", "                \"ADDITIONAL INSTRUCTIONS\": \"\",\n", "                \"INSPIRATION SEEDS\": test_cases_data[\"inspiration_seeds\"][idx],\n", "            }\n", "            for idx in range(max_cases)\n", "        ],\n", "    )\n", "\n", "    logger.debug(f\"Processing {len(iteration_df)} cases\")\n", "    results_df = generate_master_prompt_template_output_parallel(\n", "        df=iteration_df[[\"COMPILED MASTER PROMPT TEMPLATE\"]],\n", "        response_col=\"LLM RESPONSE\",\n", "    )\n", "    logger.debug(\"Generated responses with columns: {list(results_df.columns)}\")\n", "    cot_columns_to_keep = []\n", "    for col in list(results_df.columns):\n", "        if col not in [\n", "            \"LLM RESPONSE\",\n", "            \"COMPILED MASTER PROMPT TEMPLATE\",\n", "        ]:\n", "            new_col = f\"<{col.lower()}>\"\n", "            results_df[new_col] = results_df[col]\n", "            cot_columns_to_keep.append(new_col)\n", "        else:\n", "            results_df[col] = results_df[col]\n", "\n", "    # Replace NaN values with empty strings to avoid PostgreSQL JSON errors\n", "    iteration_df = iteration_df.fillna(\"\")\n", "    additional_instructions = (\n", "        iteration_df[\"ADDITIONAL INSTRUCTIONS\"].dropna().unique().tolist()\n", "    )\n", "    if additional_instructions:\n", "        iteration_df[\"COMPILED MASTER PROMPT TEMPLATE\"] = update_compiled_master_prompt_template(\n", "            list(iteration_df[\"COMPILED MASTER PROMPT TEMPLATE\"]), \"\\n\".join(additional_instructions)\n", "        )\n", "        logger.info(\n", "            \"Updated compiled master prompt template with additional instructions: {additional_instructions}\"\n", "        )\n", "    return iteration_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# **Main**"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-03-26 19:00:46.014 WARNING streamlit.runtime.scriptrunner_utils.script_run_context: Thread 'MainThread': missing ScriptRunContext! This warning can be ignored when running in bare mode.\n", "2025-03-26 19:00:46.015 WARNING streamlit.runtime.scriptrunner_utils.script_run_context: Thread 'MainThread': missing ScriptRunContext! This warning can be ignored when running in bare mode.\n", "\u001b[32m2025-03-26 19:00:46.016\u001b[0m | \u001b[1mINFO    \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgenerate\u001b[0m:\u001b[36m113\u001b[0m - \u001b[1m[Project: unknown] Starting LLM generation with model: claude-3-5-sonnet-20240620\u001b[0m\n", "2025-03-26 19:00:46.016 WARNING streamlit.runtime.scriptrunner_utils.script_run_context: Thread 'MainThread': missing ScriptRunContext! This warning can be ignored when running in bare mode.\n", "2025-03-26 19:00:46.017 WARNING streamlit.runtime.scriptrunner_utils.script_run_context: Thread 'MainThread': missing ScriptRunContext! This warning can be ignored when running in bare mode.\n", "\u001b[32m2025-03-26 19:00:46.017\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mcheck_server_capacity_and_skip_reruns_past_3_if_busy\u001b[0m:\u001b[36m68\u001b[0m - \u001b[34m\u001b[1m[Project: unknown] Checking server capacity\u001b[0m\n", "\u001b[32m2025-03-26 19:00:46.017\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mcheck_server_capacity_and_skip_reruns_past_3_if_busy\u001b[0m:\u001b[36m104\u001b[0m - \u001b[34m\u001b[1m[Project: unknown] Server status check - Consecutive errors: 0\u001b[0m\n", "\u001b[32m2025-03-26 19:00:46.018\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgenerate\u001b[0m:\u001b[36m126\u001b[0m - \u001b[34m\u001b[1m[Project: unknown] Attempt 1 with temperature 0.0\u001b[0m\n", "\u001b[32m2025-03-26 19:00:46.018\u001b[0m | \u001b[34m\u001b[1mDEBUG   \u001b[0m | \u001b[36m__main__\u001b[0m:\u001b[36mgenerate\u001b[0m:\u001b[36m136\u001b[0m - \u001b[34m\u001b[1m[Project: unknown] Sending request to LLM\u001b[0m\n"]}], "source": ["project_id = \"123\"\n", "initial_instructions: str = \"classify animals\"\n", "input_examples: str = \"None\"\n", "input_variables: str = \"None\"\n", "output_classes: str = \"None\"\n", "classifier_type: str = \"None\"\n", "\n", "enhanced_initial_instructions = generate_advanced_initial_instructions_from_llm(\n", "    initial_instructions,\n", "    classifier_type,\n", "    output_classes,\n", "    input_variables,\n", "    ENHANCED_INITIAL_INSTRUCTIONS_PROMPT_TEMPLATE,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["(current_values, master_prompt, llm_scratchpad) = generate_master_prompt_from_llm(\n", "    initial_instructions,\n", "    enhanced_initial_instructions,\n", "    input_examples,\n", "    input_variables,\n", "    classifier_type,\n", "    output_classes,\n", "    MASTER_PROMPT_TEMPLATE,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_case_generation_logic = \"generate 10 good test cases\"\n", "\n", "test_cases_data = get_test_case_generator(\n", "    enhanced_initial_instructions,\n", "    classifier_type,\n", "    output_classes,\n", "    input_variables,\n", "    master_prompt,\n", "    test_case_generation_logic,\n", "    [],\n", "    False,\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["additional_instructions = \"\"\n", "\n", "iteration_data = get_iteration_page(master_prompt, test_cases_data, additional_instructions)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 2}