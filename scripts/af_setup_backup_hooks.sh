#!/bin/bash
# af_setup_backup_hooks.sh - Configure git hooks for backup behavior

set -euo pipefail

HOOK_FILE=".git/hooks/pre-commit"
BACKUP_SCRIPT="./scripts/af_backup_gitignored_files.sh"

show_usage() {
    echo "Usage: $0 {warn|block|disable}"
    echo ""
    echo "  warn     - Warn if backup fails but allow commit (default)"
    echo "  block    - Block commit if backup fails"
    echo "  disable  - Remove backup from pre-commit hook"
}

setup_warn_mode() {
    cat > "$HOOK_FILE" << 'EOF'
#!/bin/bash
# Auto-backup gitignore and config files before commit

echo "🔄 Auto-backing up config files..."

# Capture both stdout and stderr, and the exit code
backup_output=$(./scripts/af_backup_gitignored_files.sh backup 2>&1)
backup_exit_code=$?

if [ $backup_exit_code -eq 0 ]; then
    echo "✅ Backup successful"
else
    echo "⚠️  WARNING: Backup failed!"
    echo "   Exit code: $backup_exit_code"
    echo "   Output: $backup_output"
    echo ""
    echo "   Your commit will proceed, but config files were NOT backed up."
    echo "   You may want to run 'just af-backup' manually after commit."
    echo ""
fi
EOF
    chmod +x "$HOOK_FILE"
    echo "✅ Pre-commit hook set to WARN mode (commit allowed even if backup fails)"
}

setup_block_mode() {
    cat > "$HOOK_FILE" << 'EOF'
#!/bin/bash
# Auto-backup gitignore and config files before commit

echo "🔄 Auto-backing up config files..."

# Capture both stdout and stderr, and the exit code
backup_output=$(./scripts/af_backup_gitignored_files.sh backup 2>&1)
backup_exit_code=$?

if [ $backup_exit_code -eq 0 ]; then
    echo "✅ Backup successful"
else
    echo "❌ ERROR: Backup failed!"
    echo "   Exit code: $backup_exit_code"
    echo "   Output: $backup_output"
    echo ""
    echo "   COMMIT BLOCKED - backup must succeed before commit."
    echo "   Fix the backup issue or run: just af-backup-mode warn"
    echo ""
    exit 1
fi
EOF
    chmod +x "$HOOK_FILE"
    echo "✅ Pre-commit hook set to BLOCK mode (commit blocked if backup fails)"
}

disable_backup() {
    if [ -f "$HOOK_FILE" ]; then
        rm "$HOOK_FILE"
        echo "✅ Backup pre-commit hook disabled"
    else
        echo "⚠️  No pre-commit hook found"
    fi
}

case "${1:-}" in
    warn)
        setup_warn_mode
        ;;
    block)
        setup_block_mode
        ;;
    disable)
        disable_backup
        ;;
    *)
        show_usage
        exit 1
        ;;
esac