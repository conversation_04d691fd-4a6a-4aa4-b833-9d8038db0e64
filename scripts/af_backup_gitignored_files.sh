#!/bin/bash
# af_backup_gitignored_files.sh - Personal backup/restore for gitignored files
# <PERSON>'s personal utility for backing up .gitignore and other config files

set -euo pipefail

BACKUP_DIR=".tmp/af_0/gitignore_backups"
PROJECT_NAME="pfc"
DATE=$(date +%Y%m%d)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

init_backup_repo() {
    if [ ! -d "$BACKUP_DIR" ]; then
        log "Initializing backup repository at $BACKUP_DIR"
        mkdir -p "$BACKUP_DIR"
        (cd "$BACKUP_DIR" && git init)
        # Add .tmp to gitignore so backup dir is ignored
        if [ ! -f ".gitignore" ] || ! grep -q "^\.tmp/" ".gitignore"; then
            log "Backup directory will be gitignored"
        fi
        log "Backup repository initialized"
    fi
}

backup_files() {
    log "Starting backup for project: $PROJECT_NAME"
    
    local PROJECT_DIR="$PWD"
    init_backup_repo
    
    # Make sure we're in the backup directory
    cd "$PROJECT_DIR/$BACKUP_DIR"
    
    # Backup .gitignore
    if [ -f "$PROJECT_DIR/.gitignore" ]; then
        cp "$PROJECT_DIR/.gitignore" "${PROJECT_NAME}-gitignore-${DATE}"
        log "Backed up .gitignore to ${PROJECT_NAME}-gitignore-${DATE}"
    else
        warn ".gitignore not found in project directory: $PROJECT_DIR"
    fi
    
    # Backup other common config files
    local files_to_backup=(
        ".env.example"
        ".vscode/settings.json"
        ".cursor/settings.json"
        ".sourcery.yaml"
    )
    
    for file in "${files_to_backup[@]}"; do
        if [ -f "$PROJECT_DIR/$file" ]; then
            local backup_name="${PROJECT_NAME}-$(basename "$file")-${DATE}"
            cp "$PROJECT_DIR/$file" "$backup_name"
            log "Backed up $file to $backup_name"
        fi
    done
    
    # Commit backup
    git add -A
    if git diff --staged --quiet; then
        warn "No changes to backup"
    else
        git commit -m "$PROJECT_NAME backup $(date +'%Y-%m-%d %H:%M:%S')"
        log "Backup committed successfully"
    fi
    
    log "Backup complete. Files stored in: $BACKUP_DIR"
}

restore_files() {
    log "Starting restore for project: $PROJECT_NAME"
    
    local PROJECT_DIR="$PWD"
    
    if [ ! -d "$BACKUP_DIR" ]; then
        error "Backup directory not found: $BACKUP_DIR"
    fi
    
    cd "$PROJECT_DIR/$BACKUP_DIR"
    
    # List available backups
    echo "Available backups:"
    ls -1 "${PROJECT_NAME}"-* 2>/dev/null | sort -r | head -10
    
    read -p "Enter filename to restore (or press Enter for latest .gitignore): " filename
    
    if [ -z "$filename" ]; then
        # Find latest gitignore backup
        filename=$(ls -1 "${PROJECT_NAME}-gitignore-"* 2>/dev/null | sort -r | head -1)
        if [ -z "$filename" ]; then
            error "No gitignore backups found"
        fi
        log "Using latest backup: $filename"
    fi
    
    if [ ! -f "$filename" ]; then
        error "Backup file not found: $filename"
    fi
    
    # Determine target file
    local target_file
    if [[ "$filename" =~ gitignore ]]; then
        target_file=".gitignore"
    elif [[ "$filename" =~ settings.json ]]; then
        if [[ "$filename" =~ vscode ]]; then
            target_file=".vscode/settings.json"
        elif [[ "$filename" =~ cursor ]]; then
            target_file=".cursor/settings.json"
        fi
    elif [[ "$filename" =~ sourcery.yaml ]]; then
        target_file=".sourcery.yaml"
    elif [[ "$filename" =~ env.example ]]; then
        target_file=".env.example"
    else
        read -p "Enter target filename: " target_file
    fi
    
    # Create directory if needed
    local target_dir=$(dirname "$PROJECT_DIR/$target_file")
    if [ ! -d "$target_dir" ]; then
        mkdir -p "$target_dir"
        log "Created directory: $target_dir"
    fi
    
    # Backup current file if exists
    if [ -f "$PROJECT_DIR/$target_file" ]; then
        cp "$PROJECT_DIR/$target_file" "$PROJECT_DIR/${target_file}.backup-$(date +%Y%m%d-%H%M%S)"
        log "Current file backed up as ${target_file}.backup-$(date +%Y%m%d-%H%M%S)"
    fi
    
    # Restore file
    cp "$filename" "$PROJECT_DIR/$target_file"
    log "Restored $filename to $target_file"
}

list_backups() {
    if [ ! -d "$BACKUP_DIR" ]; then
        error "Backup directory not found: $BACKUP_DIR"
    fi
    
    local PROJECT_DIR="$PWD"
    cd "$PROJECT_DIR/$BACKUP_DIR"
    log "Available backups in $BACKUP_DIR:"
    ls -la "${PROJECT_NAME}"-* 2>/dev/null | sort -k9 -r || warn "No backups found"
    
    echo ""
    log "Git history:"
    git log --oneline 2>/dev/null || warn "No git history found"
}

show_usage() {
    echo "Usage: $0 {backup|restore|list}"
    echo ""
    echo "Commands:"
    echo "  backup   - Backup .gitignore and config files to private repo"
    echo "  restore  - Restore files from backup"
    echo "  list     - List available backups"
    echo ""
    echo "Files backed up: .gitignore, .env.example, .vscode/settings.json, .cursor/settings.json, .sourcery.yaml"
}

case "${1:-}" in
    backup)
        backup_files
        ;;
    restore)
        restore_files
        ;;
    list)
        list_backups
        ;;
    *)
        show_usage
        exit 1
        ;;
esac