#!/usr/bin/env python3
"""refactor-001 - Automated refactoring using Claude Code and architecture rules

1. Load regression tests as success criteria
2. Apply architecture rules systematically
3. Refactor incrementally with continuous testing

Why: Ensures consistent application of architecture principles
How: Calls Claude Code programmatically with rule-based prompts

Requirements:
☑️ Basic prompt structure
🟠 Integration with Claude Code CLI
⏳ Regression test runner integration
⏳ Automatic rollback on test failure

Control Flow:
tests → analyze → plan → refactor → verify → commit
"""

import subprocess
import sys
from dataclasses import dataclass
from pathlib import Path


@dataclass
class RefactorTask:
    """Represents a single refactoring task"""
    file_path: Path
    rule_violations: list[str]
    priority: int  # 1=high, 2=medium, 3=low


class RefactorAgent:
    """Orchestrates refactoring with <PERSON> Code"""

    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.rules_dir = project_root / ".cursor" / "rules"
        self.regression_tests = self._discover_tests()

    def _discover_tests(self) -> list[Path]:
        """Find all regression test files"""
        test_dir = self.project_root / "tests"
        return list(test_dir.rglob("test_*.py"))

    def analyze_codebase(self) -> list[RefactorTask]:
        """Analyze code against architecture rules"""
        # This would call Claude to analyze files
        prompt = self._build_analysis_prompt()

        # Placeholder for actual Claude Code integration
        # In practice, this would call: subprocess.run(["cc", "chat", prompt])

        return []  # Would return actual violations

    def _build_analysis_prompt(self) -> str:
        """Build prompt for codebase analysis"""
        return f"""
        Analyze the codebase at {self.project_root} against these architecture rules:
        
        1. FCIS (Functional Core, Imperative Shell)
           - Find functions mixing I/O with business logic
           - Identify impure functions that should be pure
           
        2. Exception Handling
           - Find try/except blocks outside of main()
           - Identify defensive programming that should be assertions
           
        3. Pydantic Compliance  
           - Find models without strict mode
           - Find Optional fields in intermediate models
           - Find models > 25 lines
           
        4. Code Clarity
           - Find helper functions > 2 levels deep
           - Find abstractions that obscure understanding
           - Find missing EOL comments explaining WHY
           
        5. Documentation
           - Find functions without pyramid principle docstrings
           - Find missing control flow diagrams
           
        For each violation, report:
        - File path and line number
        - Specific rule violated
        - Suggested fix
        """

    def create_refactor_plan(self, tasks: list[RefactorTask]) -> str:
        """Create detailed refactoring plan"""
        plan = ["# Refactoring Plan\n"]

        # Group by priority
        high_priority = [t for t in tasks if t.priority == 1]
        medium_priority = [t for t in tasks if t.priority == 2]
        low_priority = [t for t in tasks if t.priority == 3]

        if high_priority:
            plan.append("## High Priority (Breaks Architecture)\n")
            for task in high_priority:
                plan.append(f"- [ ] {task.file_path}: {', '.join(task.rule_violations)}")

        if medium_priority:
            plan.append("\n## Medium Priority (Clarity Issues)\n")
            for task in medium_priority:
                plan.append(f"- [ ] {task.file_path}: {', '.join(task.rule_violations)}")

        if low_priority:
            plan.append("\n## Low Priority (Style/Documentation)\n")
            for task in low_priority:
                plan.append(f"- [ ] {task.file_path}: {', '.join(task.rule_violations)}")

        return "\n".join(plan)

    def refactor_file(self, task: RefactorTask) -> bool:
        """Refactor a single file"""
        prompt = f"""
        Refactor {task.file_path} to fix these violations:
        {', '.join(task.rule_violations)}
        
        Rules to follow:
        - Apply FCIS: separate pure functions from I/O
        - Remove all try/except from business logic
        - Use assertions for preconditions
        - Keep helper functions self-explanatory
        - Add pyramid principle docstrings
        - Add EOL comments explaining WHY
        
        Make minimal changes to fix issues.
        Preserve all existing functionality.
        """

        # Would call: subprocess.run(["cc", "edit", str(task.file_path), prompt])

        # Run tests after each change
        return self.run_regression_tests()

    def run_regression_tests(self) -> bool:
        """Run regression tests to verify refactoring"""
        for test_file in self.regression_tests:
            result = subprocess.run(
                ["python", "-m", "pytest", str(test_file), "-v"],
                check=False, capture_output=True,
                text=True
            )
            if result.returncode != 0:
                print(f"❌ Tests failed in {test_file}")
                print(result.stdout)
                return False

        print("✅ All regression tests passed")
        return True

    def commit_changes(self, message: str):
        """Commit successful refactoring"""
        subprocess.run(["git", "add", "-A"], check=False)
        subprocess.run(["git", "commit", "-m", f"refactor: {message} -Claude"], check=False)


def main():
    """Main refactoring workflow"""
    project_root = Path.cwd()
    agent = RefactorAgent(project_root)

    print("🔍 Analyzing codebase against architecture rules...")
    tasks = agent.analyze_codebase()

    if not tasks:
        print("✨ No architecture violations found!")
        return

    print(f"\n📋 Found {len(tasks)} files needing refactoring")
    plan = agent.create_refactor_plan(tasks)
    print(plan)

    # Write plan to zTasks.md
    tasks_file = project_root / "src" / "pfc" / "tasks" / "ztasks.md"
    with open(tasks_file, "a") as f:
        f.write(f"\n\n## Refactoring Plan - {Path.cwd().name}\n")
        f.write(plan)

    print("\n🔧 Starting incremental refactoring...")
    for i, task in enumerate(tasks, 1):
        print(f"\n[{i}/{len(tasks)}] Refactoring {task.file_path}")

        if agent.refactor_file(task):
            agent.commit_changes(f"fix {task.file_path.name} - {task.rule_violations[0]}")
        else:
            print("❌ Refactoring failed, rolling back")
            subprocess.run(["git", "checkout", str(task.file_path)], check=False)
            sys.exit(1)

    print("\n🎉 Refactoring complete! All tests passing.")


if __name__ == "__main__":
    main()
