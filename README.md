# PFC - Prompt Flow Creator

A comprehensive AI pipeline system for prompt engineering, evaluation, and optimization.

## Quick Start

```bash
# Install dependencies using uv
just setup

# Run linting
just lint

# Generate requirements.txt (for pip users)
just compile-deps
```

## Project Structure

```
pfc/
   src/pfc/          # Main package
      core/         # Core utilities and pipeline
      doc_gen/      # Documentation generation
      evaluate/     # Test case evaluation
      optimize/     # Prompt optimization
      refine/       # Prompt refinement
      ship/         # Final delivery
   docs/             # Documentation
   notebooks/        # Jupyter notebooks
   tasks/           # Task tracking
```

## MCP (Model Context Protocol) Configuration

This project includes MCP server configurations for enhanced AI-assisted development.

### VSCode/Cursor Configuration

Two configuration files are provided:

1. **`.vscode/mcp.json`** - Workspace configuration (tracked in git)
   - Shareable with team members
   - Contains common MCP servers for the project
   - Sensitive data handled via input prompts

2. **`.cursor/mcp.json`** - Cursor-specific configuration (not tracked)
   - For individual developer customization
   - May contain personal MCP servers

### Available MCP Servers

- **filesystem** - Local file system access
- **memory** - Context persistence across sessions
- **context7** - Real-time, version-aware documentation for libraries
  - Automatically fetches up-to-date documentation and code examples
  - Usage: Append "use context7" to your prompts
  - Example: "Create a FastAPI endpoint use context7"
- **github** - GitHub repository integration (disabled by default)
- **sqlite** - Database access (Cursor only, disabled by default)
- **python** - Python code execution (Cursor only, disabled by default)

### Setting Up MCP

1. **First Time Setup**:
   - Open the project in VSCode/Cursor
   - You'll be prompted for any required API keys
   - Keys are stored securely by the editor

2. **Enable Optional Servers**:
   - Edit the MCP configuration file
   - Remove `"disabled": true` from servers you want to use
   - Restart your editor

3. **Add Custom Servers**:
   ```json
   "my-server": {
     "type": "stdio",
     "command": "npx",
     "args": ["-y", "@my-org/mcp-server"],
     "env": {
       "API_KEY": "${input:my_api_key}"
     }
   }
   ```

### Security Notes

- Never commit API keys or sensitive data
- Use input variables for credentials
- Review MCP server code before enabling
- Only install servers from trusted sources

## Development

### Using uv (Recommended)

```bash
# Add a dependency
uv add package-name

# Add a dev dependency
uv add --dev package-name

# Sync dependencies
uv sync
```

### Virtual Environment

This project uses `.venv` for the virtual environment (not `venv`).

### Code Style

- Ruff for linting and formatting
- Line length: 150 characters
- See `pyproject.toml` for full configuration

## Testing

```bash
# Run tests (when available)
just test
```

## Contributing

1. Follow the conventions in `CLAUDE.md`
2. Use the task tracking system in `tasks/ztasks.md`
3. Commit regularly with descriptive messages
4. Run `just lint` before committing

## License

[Add license information here]# Test automated backup
# Test backup warning
# Test block mode
