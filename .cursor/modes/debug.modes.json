{"name": "debug", "description": "Debugging mode with relaxed brittleness for troubleshooting", "rules": ["01_development-workflow-agent.mdc", "02_code-and-style-agent.mdc", "03_project-conventions-agent.mdc"], "context": {"allow": ["Temporary try/except for debugging", "Verbose logging with rich prints", "Exploration in .tmp/ directory", "Defensive checks to isolate issues"], "workflow": ["Use .temp files for experimental code", "Add rich.print statements liberally", "Track debugging steps in zTasks.md", "Remove all debug code before commit"], "remember": ["Debug code never stays in business logic", "Convert findings to regression tests", "Document root cause in comments"]}}