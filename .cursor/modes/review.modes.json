{"name": "review", "description": "Code review mode focusing on architecture compliance and documentation", "rules": ["00_core-principles-always.mdc", "04_pydantic-law-agent.mdc", "05_pipeline-architecture-agent.mdc", "06_documentation-standards-agent.mdc"], "context": {"check": ["FCIS architecture compliance", "Business logic clarity and purity", "Pydantic strict mode usage", "Pyramid principle in docstrings", "Pipeline pattern consistency", "No exception handling in business logic"], "verify": ["All models ≤ 25 lines", "Helper functions are self-explanatory", "EOL comments explain WHY not WHAT", "Control flow diagrams in docstrings", "Regression test coverage"], "report": ["Architecture violations", "Missing documentation", "Complexity that should be refactored", "Opportunities for code culling"]}}