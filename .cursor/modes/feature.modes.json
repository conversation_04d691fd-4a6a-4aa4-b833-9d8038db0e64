{"name": "feature", "description": "Feature development mode balancing speed with architecture compliance", "rules": ["00_core-principles-always.mdc", "01_development-workflow-agent.mdc", "02_code-and-style-agent.mdc", "03_project-conventions-agent.mdc", "04_pydantic-law-agent.mdc"], "context": {"approach": ["Start with clear pipeline design", "Define Pydantic models first", "Write regression tests early", "Implement with brittle code", "Abstract only when it improves clarity"], "workflow": ["Update zTasks.md with feature plan", "Create models with strict validation", "Implement functional core", "Add imperative shell last", "Document with pyramid principle"], "patterns": ["New features as new functions", "Preserve existing business logic clarity", "Use pipeline pattern for multi-step features", "Rich prints for feature-specific logging"]}}