{"name": "refactor", "description": "Strict refactoring mode emphasizing FCIS, brittle code, and regression testing", "rules": ["00_core-principles-always.mdc", "01_development-workflow-agent.mdc", "02_code-and-style-agent.mdc", "03_project-conventions-agent.mdc", "04_pydantic-law-agent.mdc", "05_pipeline-architecture-agent.mdc", "06_documentation-standards-agent.mdc"], "context": {"focus": ["FCIS architecture compliance", "Remove all exception handling from business logic", "Ensure brittle, minimalistic code", "Run regression tests continuously", "Update zTasks.md throughout", "Commit after each successful change"], "enforce": ["No try/except outside main()", "<PERSON><PERSON><PERSON> liberally for preconditions", "Keep helper functions max 2 levels deep", "All Pydantic models in strict mode", "Business logic must be pure functions"], "workflow": ["Read existing regression tests first", "Analyze code against architecture rules", "Plan refactoring in zTasks.md", "Refactor incrementally with tests", "Document changes in docstrings"]}}