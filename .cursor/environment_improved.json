{"agentCanUpdateSnapshot": true, "install": "apt-get update && apt-get install -y just && curl -LsSf https://astral.sh/uv/install.sh | sh && echo 'export PATH=\"$HOME/.cargo/bin:$PATH\"' >> ~/.bashrc", "start": "source ~/.bashrc && just setup-dev", "terminals": [{"name": "Development Server", "command": "source ~/.bashrc && uv run uvicorn src.pfc.core.c3_pipeline:app --reload --host 0.0.0.0 --port 8000", "description": "Runs the FastAPI development server on port 8000"}], "ports": [{"name": "FastAPI", "port": 8000}]}