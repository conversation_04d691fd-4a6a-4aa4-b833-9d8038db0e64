> and just and uv are pre-installed on the ubuntu instance? Check online and docs again.

You've raised an excellent and crucial point. My apologies. I made an assumption that the environment for the background agent would mirror the local setup, but that is incorrect. The agent runs in a fresh, standardized Ubuntu environment which does not come with project-specific tools like `just` and `uv` pre-installed. You are right to question this.

Let's correct this. The Cursor documentation states that agents run on an "isolated ubuntu-based machine" and that you're responsible for installing dependencies. This means we must handle the installation of `just` and `uv` ourselves within the setup script.

I will now search for the standard installation methods for these tools on Ubuntu to construct a correct `install` command.
Ran tool
The search results confirm our line of thinking.

1. **Installing `uv`**: The official documentation from Astral (`https://docs.astral.sh/uv/getting-started/installation/`) provides several installation methods. For a non-interactive script on a fresh Linux machine, the standalone shell script installer is the most robust and recommended method:
   `curl -LsSf https://astral.sh/uv/install.sh | sh`
   This command is self-contained and handles the installation without further input.
2. **Installing `just`**: The search results did not provide a definitive, one-size-fits-all command for `just`. While `apt` is common, it may not be available or have the latest version. A more reliable method for CI/CD or containerized environments is often to download a pre-compiled binary. However, for simplicity and since the background agent is a standard Ubuntu image, we can try to install it with `apt-get`, which is the standard package manager for Ubuntu. We will need to run `apt-get update` first.

Therefore, I will construct a multi-step `install` command for `.cursor/environment.json` that first installs `just` using `apt-get` and then installs `uv` using its official script, before finally running our project's setup.

I will now update the `.cursor/environment.json` file with the correct, comprehensive installation command.
I have updated the `.cursor/environment.json` file with a more robust installation script.

However, looking at the `justfile`, the `setup-dev` command is `uv sync --dev` followed by running `compile_requirements.sh`. It seems I've essentially inlined the `just setup-dev` command into the `environment.json` and also added the `just` install step.

Let's simplify this. The `uv` installer script adds `uv` to the `PATH` in `~/.cargo/bin`. `just` will be installed system-wide. The original idea of installing the tools and then calling `just setup-dev` is cleaner and more maintainable. If you change the setup process in the `justfile`, you won't have to update `.cursor/environment.json` as well.

I will correct the `install` command to reflect this better practice. It will install the tools, then call the `just` command. Note that the `uv` executable will be in `/root/.cargo/bin/` in the agent's environment, so we need to make sure that's in the `PATH` or call it with its full path. The `install.sh` script for `uv` should handle the `PATH`, but to be safe, I'll adjust the command. The `sh` installer for `uv` will print where it installs the binary. The default location for the root user is typically `/root/.cargo/bin`.

I'll refine the command to be more robust.
Okay, that's much cleaner. The `.cursor/environment.json` file is now correctly configured.

Here is the final breakdown:

### What This Command Does

The `install` command in `.cursor/environment.json` will now execute the following steps in the background agent's environment:

1. `apt-get update`: Refreshes the package list for the Ubuntu environment.
2. `apt-get install -y just`: Installs the `just` command runner non-interactively.
3. `curl -LsSf https://astral.sh/uv/install.sh | sh`: Downloads and runs the official installer for `uv`, which is the project's Python package manager.
4. `export PATH="$HOME/.cargo/bin:$PATH"`: Updates the shell's `PATH` to include the directory where `uv` was just installed, ensuring it can be found in the next step.
5. `just setup-dev`: Executes the setup command from your `justfile`, which handles the actual dependency installation for the project.

### How to Test

As before, the way to test this is to run a background agent:

1. Open the **Background Agent** panel (`Ctrl+E` or `Cmd+E`).
2. Give it a task, such as: "Run all tests."

The agent will now use the updated installation command. If it successfully completes the setup and runs the test, the environment is configured correctly. This setup is now robust and properly separates the tool installation from the project-specific setup, making it easier to maintain.

# Cursor – Background Agents

With background agents, spawn asynchronous agents that edit and run code in a remote environment. View their status, send follow-ups, or take over anytime.

How to Use
----------

1. Hit to open the background agent control panel to list agents, spawn new ones, and view status.
2. After submitting a prompt, select your agent from the list to view status and enter the machine.

Feedback
--------

Send feedback to [our Discord #background-agent channel](https://discord.gg/jfgpZtYpmb) or [<EMAIL>](mailto:<EMAIL>). Please send bugs, feature requests or ideas.

Setup
-----

Background agents run in an isolated ubuntu-based machine by default. Agents have internet access and can install packages.

#### GitHub connection

Background agents clone your repo from GitHub and work on a separate branch, pushing to your repo for easy handoff.

Grant read-write privileges to your repo (and any dependent repos or submodules). We’ll support other providers (GitLab, BitBucket, etc) in the future.

#### Base Environment Setup

For advanced cases, set up the environment yourself. Get an IDE instance connected to the remote machine. Set up your machine, install tools and packages, then take a snapshot. Configure runtime settings:

* Install command runs before an agent starts and installs runtime dependencies. This might mean running `npm install` or `bazel build`.
* Terminals run background processes while the agent works - like starting a web server or compiling protobuf files.

For the most advanced cases, use a Dockerfile for machine setup. The dockerfile lets you set up system-level dependencies: install specific compiler versions, debuggers, or switch the base OS image. Don’t `COPY` the entire project - we manage the workspace and check out the correct commit. Still handle dependency installation in the install script.

Enter any required secrets for your dev environment - they’re stored encrypted-at-rest (using KMS) in our database and provided in the background agent environment.

The machine setup lives in `.cursor/environment.json`, which can be committed in your repo (recommended) or stored privately. The setup flow guides you through creating `environment.json`.

#### Maintenance Commands

When setting up a new machine, we start from the base environment, then run the `install` command from your `environment.json`. This command is what a developer would run when switching branches - install any new dependencies.

For most people, the `install` command is `npm install` or `bazel build`.

To ensure fast machine startup, we cache disk state after the `install` command runs. Design it to run multiple times. Only disk state persists from the `install` command - processes started here won’t be alive when the agent starts.

#### Startup Commands

After running `install`, the machine starts and we run the `start` command followed by starting any `terminals`. This starts processes that should be alive when the agent runs.

The `start` command can often be skipped. Use it if your dev environment relies on docker - put `sudo service docker start` in the `start` command.

`terminals` are for app code. These terminals run in a `tmux` session available to you and the agent. For example, many website repos put `npm run watch` as a terminal.

#### The `environment.json` Spec

The `environment.json` file can look like:

```json
{
  "snapshot": "POPULATED_FROM_SETTINGS",
  "install": "npm install",
  "terminals": [
    {
      "name": "Run Next.js",
      "command": "npm run dev"
    }
  ]
}
```

Formally, the spec is [defined here](https://www.cursor.com/schemas/environment.schema.json).

Models
------

Only [Max Mode](https://docs.cursor.com/context/max-mode)-compatible models are available for background agents.

Pricing
-------

Learn more about [Background Agent pricing](https://docs.cursor.com/account/pricing#background-agent).

Security
--------

Background Agents are available in Privacy Mode. We never train on your code and only retain code for running the agent. [Learn more about Privacy mode](https://www.cursor.com/privacy-overview).

What you should know:

1. Grant read-write privileges to our GitHub app for repos you want to edit. We use this to clone the repo and make changes.
2. Your code runs inside our AWS infrastructure in isolated VMs and is stored on VM disks while the agent is accessible.
3. The agent has internet access.
4. The agent auto-runs all terminal commands, letting it iterate on tests. This differs from the foreground agent, which requires user approval for every command. Auto-running introduces data exfiltration risk: attackers could execute prompt injection attacks, tricking the agent to upload code to malicious websites. See [OpenAI’s explanation about risks of prompt injection for background agents](https://platform.openai.com/docs/codex/agent-network#risks-of-agent-internet-access).
5. If privacy mode is disabled, we collect prompts and dev environments to improve the product.
6. If you disable privacy mode when starting a background agent, then enable it during the agent’s run, the agent continues with privacy mode disabled until it completes.

```json
{
  "$schema": "https://json-schema.org/draft/2019-09/schema",
  "description": "Defines a dev environment",
  "allowComments": true,
  "allowTrailingCommas": false,
  "definitions": {
    "common": {
      "type": "object",
      "properties": {
        "name": {
          "type": "string",
          "description": "The name of the environment."
        },
        "user": {
          "type": "string",
          "description": "The user to run the environment as."
        },
        "install": {
          "type": "string",
          "description": "The install command to run when the environment is started."
        },
        "start": {
          "type": "string",
          "description": "The start command to run when the environment is started."
        },
        "repositoryDependencies": {
          "type": "array",
          "description": "Repositories that are required for the environment to work, and need to be included in the GitHub access token that is generated for the environment.",
          "items": {
            "type": "string",
            "description": "The URL of the dependent repository, e.g. `github.com/org/repo`."
          }
        },
        "ports": {
          "type": "array",
          "description": "Ports to expose from the container. Similar to devcontainers port forwarding.",
          "items": {
            "type": "object",
            "required": ["port"],
            "properties": {
              "name": {
                "type": "string",
                "description": "A descriptive name for the port (e.g., 'web server', 'api')."
              },
              "port": {
                "type": "integer",
                "minimum": 1,
                "maximum": 65535,
                "description": "The port number inside the container to expose."
              }
            }
          }
        },
        "terminals": {
          "type": "array",
          "description": "The terminals to run when the environment is started.",
          "items": {
            "oneOf": [
              {
                "type": "array",
                "items": {
                  "type": "object",
                  "required": ["command"],
                  "properties": {
                    "name": {
                      "type": "string",
                      "description": "The name of the terminal."
                    },
                    "command": {
                      "type": "string",
                      "description": "The command to run in the terminal."
                    },
                    "description": {
                      "type": "string",
                      "description": "A description of what the terminal does. This is displayed to the agent."
                    }
                  }
                }
              },
              {
                "type": "object",
                "required": ["command"],
                "properties": {
                  "name": {
                    "type": "string",
                    "description": "The name of the terminal."
                  },
                  "command": {
                    "type": "string",
                    "description": "The command to run in the terminal."
                  },
                  "description": {
                    "type": "string",
                    "description": "A description of what the terminal does. This is displayed to the agent."
                  }
                }
              }
            ]
          }
        }
      }
    },
    "container": {
      "type": "object",
      "properties": {
        "build": {
          "type": "object",
          "description": "Docker build-related options.",
          "properties": {
            "dockerfile": {
              "type": "string",
              "description": "The location of the Dockerfile that defines the contents of the container. The path is relative to the folder containing the `environment.json` file."
            },
            "context": {
              "type": "string",
              "description": "The location of the context folder for building the Docker image. The path is relative to the folder containing the `environment.json` file."
            }
          },
          "required": ["dockerfile"],
          "unevaluatedProperties": false
        },
        "snapshot": {
          "type": "string",
          "description": "A snapshot ID for the base environment."
        },
        "agentCanUpdateSnapshot": {
          "type": "boolean",
          "description": "Whether the agent can update the snapshot."
        }
      },
      "required": []
    }
  },
  "allOf": [
    {
      "$ref": "#/definitions/container"
    },
    {
      "$ref": "#/definitions/common"
    }
  ],
  "unevaluatedProperties": false
}
```
