{"$schema": "https://modelcontextprotocol.io/schemas/v1.0/mcp.schema.json", "comment": "UNTESTED LIKELY NOT CORRECT. Cursor-specific MCP configuration - not tracked in git by default", "inputs": [{"id": "openai_api_key", "description": "OpenAI API Key for AI pipeline", "type": "promptString", "password": true}, {"id": "anthropic_api_key", "description": "Anthropic API Key for Claude models", "type": "promptString", "password": true}], "servers": {"filesystem": {"comment": "Local filesystem access for the project", "type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem"], "env": {}}, "sqlite": {"comment": "SQLite database access for local development", "type": "stdio", "command": "uvx", "args": ["mcp-server-sqlite", "--db-path", "./data/pfc.db"], "disabled": true}, "python": {"comment": "Python code execution server", "type": "stdio", "command": "uvx", "args": ["mcp-server-python"], "env": {}, "disabled": true}, "context7": {"comment": "Real-time, version-aware documentation for libraries - append 'use context7' to prompts", "type": "stdio", "command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "env": {}}}}