# Testing Cursor Background Agent Environment

## Current environment.json
```json
{
  "agentCanUpdateSnapshot": true,
  "install": "apt-get update && apt-get install -y just && curl -LsSf https://astral.sh/uv/install.sh | sh && export PATH=\"$HOME/.cargo/bin:$PATH\" && just setup-dev"
}
```

## How to Test

1. **Create a Background Agent**:
   - Press Cmd+Shift+B (Mac) or Ctrl+Shift+B (Windows/Linux)
   - Select "Create new agent"
   - Choose your repository

2. **Monitor Installation**:
   - The agent will run the install command
   - Watch for any errors during:
     - apt-get update
     - Installing just
     - Installing uv
     - Running just setup-dev

3. **Verify Installation**:
   Once the agent is running, you can verify by having it run:
   ```bash
   which just
   which uv
   uv --version
   just --version
   ```

4. **Test Project Commands**:
   ```bash
   # Test that dependencies are installed
   uv run python -c "import pfc; print('Package installed successfully')"
   
   # Test linting
   just lint
   
   # Test other just commands
   just --list
   ```

## Common Issues & Solutions

### PATH not persisting
The current install command exports PATH only for that session. Consider updating to:

```json
{
  "agentCanUpdateSnapshot": true,
  "install": "apt-get update && apt-get install -y just && curl -LsSf https://astral.sh/uv/install.sh | sh && echo 'export PATH=\"$HOME/.cargo/bin:$PATH\"' >> ~/.bashrc && source ~/.bashrc && just setup-dev"
}
```

### Alternative: Use terminals for PATH
```json
{
  "agentCanUpdateSnapshot": true,
  "install": "apt-get update && apt-get install -y just && curl -LsSf https://astral.sh/uv/install.sh | sh",
  "start": "export PATH=\"$HOME/.cargo/bin:$PATH\" && just setup-dev",
  "terminals": [
    {
      "name": "Dev Server",
      "command": "export PATH=\"$HOME/.cargo/bin:$PATH\" && uv run uvicorn src.pfc.core.c3_pipeline:app --reload",
      "description": "Runs the FastAPI development server"
    }
  ]
}
```

## Debug Commands

If the agent fails, have it run these debug commands:
```bash
# Check if tools are installed
ls -la ~/.cargo/bin/
cat ~/.bashrc
echo $PATH

# Check project state
ls -la
cat pyproject.toml
uv pip list
```