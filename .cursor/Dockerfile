FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Install uv package manager
RUN curl -LsSf https://astral.sh/uv/install.sh | sh
ENV PATH="/root/.cargo/bin:$PATH"

# Install just command runner
RUN curl --proto '=https' --tlsv1.2 -sSfL https://just.systems/install.sh | bash -s -- --to /usr/local/bin

# Set working directory
WORKDIR /workspace

# Copy project files for dependency installation
COPY ../pyproject.toml ../requirements.txt ./

# Install Python dependencies
RUN uv sync --dev

# The workspace will be mounted here, don't copy the entire project