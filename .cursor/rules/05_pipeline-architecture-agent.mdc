---
title: "Pipeline Architecture Patterns"
description: "Rules for pipeline-based architecture and iteration patterns"
globs: ["**/pipeline.py", "**/iteration_machine*.py", "**/*_pipeline.py"]
---

## Pipeline Over DAG

- Stick to linear pipelines for clarity
- Each step transforms data and passes to next
- No complex handoffs - use router functions if needed
- All loopbacks except retry logic must be separate pipelines

## Core Definitions

### Component
- A step/stage in the pipeline
- Single responsibility
- Clear input/output types
- Maps to purple boxes in diagrams

### Node
- Data object passed between components
- Immutable by default
- Pydantic models for type safety
- Contains all context needed for processing

## Iteration Machines

### Placement Rules
- `iteration_machine_1.py` - loops on prompt only (in doc_gen)
- `iteration_machine_2.py` - loops on TC outputs vs expected (in refine)
- `iteration_machine_3.py` - loops on grader feedback (in optimize)

### Iteration Constraints
- NO unlimited loops - max 3-10 iterations
- Each exposes single `def iterate()` callable
- Callable plugs into orchestrator
- Must track iteration count and reason for stopping

## Pipeline Best Practices

### Functional Orchestration
```python
# Good - functional pipeline
result = pipe(
    input_data,
    step1_transform,
    step2_process,
    step3_validate,
    step4_output
)

# Bad - stateful orchestration
orchestrator = Orchestrator()
orchestrator.add_step(step1)
orchestrator.run()
```

### Error Handling
- Let errors bubble to orchestrator
- Orchestrator decides retry vs fail
- No try/except in pipeline steps
- Assert preconditions liberally

### Diagrammatic Sync
- Every purple box = one Python script
- Pink boxes = external services/models
- File names match diagram labels
- Comments reference diagram sections

## Router Pattern

When branching is needed:
```python
def route_by_type(node: ProcessNode) -> ProcessNode:
    """Router for type-based processing - replaces complex handoffs"""
    if node.type == "A":
        return process_type_a(node)
    elif node.type == "B":
        return process_type_b(node)
    else:
        assert False, f"Unknown type: {node.type}"
```

## Testing Pipelines

- Test each component in isolation
- Test full pipeline with known inputs/outputs
- Regression tests for each pipeline configuration
- Name tests: "if X doesn't produce Y then broken"