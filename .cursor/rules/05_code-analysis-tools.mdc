---
description: Code analysis and complexity measurement tools available
---
# Code Analysis Tools

The project includes several code analysis tools for measuring and tracking code quality.

## Available Tools

### Complexity Measurement
- **radon** - Cyclomatic complexity, maintainability index, raw metrics
  ```bash
  radon cc src/ -s -a  # Cyclomatic complexity with averages
  radon mi src/       # Maintainability index
  radon raw src/      # Raw metrics (LOC, comments, etc.)
  ```

- **xenon** - Complexity threshold enforcement
  ```bash
  xenon --max-absolute B --max-modules B --max-average A src/
  ```

- **wily** - Track complexity over time
  ```bash
  wily build src/
  wily report src/pfc/core/models.py complexity
  wily diff src/pfc/core/models.py -r HEAD~5
  ```

### Code Quality
- **pylint** - Comprehensive Python code analysis
  ```bash
  pylint src/ --disable=all --enable=R0801  # Duplication detection
  ```

- **vulture** - Dead code detection
  ```bash
  vulture src/ --min-confidence 80
  ```

- **sourcery** - Refactoring suggestions
  ```bash
  sourcery review src/ --enable refactoring
  sourcery review src/ --no-fix  # Review only
  ```

### Rich Output
- **rich** - Beautiful terminal formatting for all tools
  ```python
  from rich.console import Console
  console = Console()
  console.print("[bold green]Success![/bold green]")
  ```

## Philosophy: Measure, Don't Auto-Fix

These tools are for **measurement and awareness**, not automatic fixing:

1. **Track metrics** - Know your complexity
2. **Set thresholds** - Define acceptable limits
3. **Manual review** - Understand before changing
4. **Agent feedback** - Post issues to notification boards

## Integration Example

```python
# Future: Post complexity issues to agents
def check_complexity(file_path: Path):
    result = subprocess.run(
        ["radon", "cc", str(file_path), "--json"],
        capture_output=True,
        text=True
    )
    
    metrics = json.loads(result.stdout)
    for func in metrics:
        if func['complexity'] > 10:
            post_to_agent_board(
                agent_id=get_file_owner(file_path),
                issue={
                    'type': 'complexity',
                    'file': file_path,
                    'function': func['name'],
                    'complexity': func['complexity'],
                    'suggestion': 'Consider breaking into smaller functions'
                }
            )
```

Remember: Complexity metrics inform refactoring decisions but don't dictate them.
