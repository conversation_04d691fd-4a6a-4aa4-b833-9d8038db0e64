---
description: Vision for automated coding agent architecture and continuous refactoring
---
# Automated Agent Architecture Vision

This codebase is designed to support a future multi-agent coding system with continuous improvement.

## Architecture Goals

### 1. Multi-Agent Collaboration
- Different Claude Code agents responsible for different dirs/files
- Agents share `/rules`, `/tasks`, and `/documentation` files
- Minimal context per agent for optimization
- Re-continuable via thread IDs

### 2. Continuous Quality Improvement
- **Specs & Requirements Checkers** - Monitor code compliance
- **Documentation Agents** - Ensure docs match implementation
- **Refactoring Agents** - Reduce LOC while maintaining functionality
- **Visual UI Testing** - Screenshot-based validation

### 3. Feedback Loops (Not Auto-Fix)
- Linters post to agent "notification boards"
- Agents acknowledge and fix issues in their domain
- No automatic fixes - agents must understand the issue

### 4. Standardization
- Centralized "duplo blocks" for reference
- Enforce similar coding patterns across agents
- Visual examples: "Code like this"

### 5. Advanced Testing
- `llm_pytest()` for declarative tests using GPT-4 judges
- Regression test writing before refactoring
- Visual UI test harnesses

## Key Principles

1. **Agents Don't Mock** - Real execution, real failures
2. **Brittle is Good** - Fail fast, fail loud
3. **Types & Pipelines First** - Define contracts early
4. **Visual Validation** - Screenshots catch what code can't
5. **Incremental Refactoring** - Small, tested changes

## Current Foundation

- **Pydantic Models** - See [models.py](mdc:src/pfc/core/models.py)
- **Pipeline Architecture** - Function composition ready
- **Linting Rules** - Extensive configuration in [pyproject.toml](mdc:pyproject.toml)
- **Code Analysis Tools** - Radon, Xenon, Wily, Sourcery

## Integration Points

```python
# Future agent notification example
def post_to_agent_board(agent_id: str, issue: dict):
    """Post linting issue to agent's notification board"""
    # Agent will see: file, line, rule violated, suggestion
    
# Future visual validation
def visual_check(screenshot: Path, expected: Path):
    """LLM judge compares screenshots"""
    # GPT-4V validates UI matches expectations
```

This vision informs all architectural decisions - build for agents, not just humans.
