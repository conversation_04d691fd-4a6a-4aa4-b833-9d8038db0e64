---
description: Preferred Python elegance refactorings from Sourcery
globs: "*.py"
---
# Python Elegance Refactorings

Prefer these Sourcery refactorings to make code more Pythonic and concise:

## Iterator & Collection Patterns
- **`use-next`** - Replace for loops with `next()` for finding first match
- **`use-any`** - Replace for loops with `any()` for existence checks
- **`use-all`** - Replace for loops with `all()` for universal checks
- **`convert-to-enumerate`** - Replace manual counter with `enumerate()`
- **`use-itertools-product`** - Replace nested loops with `itertools.product()`

## Augmented Operations
- **`augmented-assign`** - Use `+=`, `-=`, `*=` instead of `x = x + 1`
- **`dict-assign-update-to-union`** - Use `|=` for dict updates (Python 3.9+)
- **`for-append-to-extend`** - Replace for loop appends with `extend()`

## String Operations
- **`use-fstring-for-concatenation`** - Replace string concatenation with f-strings
- **`use-fstring-for-formatting`** - Replace `.format()` with f-strings
- **`use-str-join`** - Replace loops building strings with `str.join()`

## Comprehensions & Generators
- **`list-comprehension`** - Convert for loops to list comprehensions
- **`dictionary-comprehension`** - Convert for loops to dict comprehensions
- **`set-comprehension`** - Convert for loops to set comprehensions
- **`yield-from`** - Replace `for x in y: yield x` with `yield from y`

## Conditional & Boolean Elegance
- **`assign-if-exp`** - Use ternary operator `x = a if condition else b`
- **`use-or-for-fallback`** - Use `value or default` pattern
- **`guard`** - Use early returns instead of nested if
- **`boolean-if-exp-identity`** - Simplify `True if x else False` to `bool(x)`

## Collection Operations
- **`default-get`** - Use `dict.get(key, default)` instead of if/else
- **`use-dictionary-items`** - Use `.items()` instead of iterating keys
- **`collection-into-set`** - Use `set()` for membership testing
- **`list-literal`** - Use `[]` instead of `list()`

## Example Transformations

```python
# Before: Manual loop
found = False
for item in items:
    if item.is_valid():
        found = True
        break

# After: use-any
found = any(item.is_valid() for item in items)

# Before: String concatenation
message = "Hello, " + name + "! Your score is " + str(score)

# After: use-fstring-for-concatenation
message = f"Hello, {name}! Your score is {score}"

# Before: Dict get with if/else
if key in data:
    value = data[key]
else:
    value = default_value

# After: default-get
value = data.get(key, default_value)
```

Run analysis: `sourcery review src/ --enable refactoring`
