---
title: "Pydantic Law - Critical Rules"
description: "Non-negotiable Pydantic usage rules for type safety and validation"
globs: ["**/*.py"]
---

## The 10 Critical "Never Bend" Pydantic Rules

### 1. Strict Mode Everywhere
```python
class Config:
    strict = True
# Or in V2:
model_config = ConfigDict(strict=True)
```

### 2. No Optional Fields in Intermediate Models
- Optional ➞ ambiguity ➞ hidden failures
- Optional allowed ONLY at system boundaries (CLI, HTTP)
- Use explicit defaults instead of Optional when possible

### 3. Validate on Assignment
```python
model_config = {"validate_assignment": True}
```

### 4. Forbid Extra Keys
```python
class Config:
    extra = "forbid"
# Or in V2:
model_config = ConfigDict(extra="forbid")
```
- Catches upstream schema drift instantly

### 5. Custom Validators Over Assert
- Use field validators for field-specific validation
- Makes error messages actionable
- Keep asserts for invariants spanning multiple fields

### 6. Model Dump Best Practices
- NEVER call .model_dump() without mode="json" or by_alias=True
- Reduces accidental type coercion
- Ensures consistent serialization

### 7. Immutable Config Objects
```python
model_config = ConfigDict(frozen=True)
```
- Guards "Business Logic is Sacred" principle
- Once validated, data can't mutate unless explicitly cloned

### 8. Version Every Major Schema
```python
class MyModel(BaseModel):
    __schema_version__ = "v1"
    # fields...
```
- Enables diff & migration tracking
- Break pipeline only when version bump is intentional

### 9. DocTests in Model Docstrings
- Show one happy path & one failure
- Doubles as specification
- Makes models self-documenting

### 10. Keep Models ≤ 25 Lines
- Long models hide complexity
- Complexity likely belongs in another Duplo block
- Compose models rather than creating mega-models

## Model Placement Rules

### Locality Over DRY
- If schema used ONLY inside one stage ➞ declare in that stage's file
- Keep code close to where it's used

### Shared Models
- If schema shared across ≥2 stages ➞ re-export from `c1_models.py`
- Prevents circular imports
- Single-hop imports for clarity

## Pydantic + TypeHints Requirements

- Use with mypy strict mode
- All fields must have type annotations
- No `Any` types without explicit justification
- Prefer Union over Optional for clarity

## Schema + Validation Only

- Only schema definition and validation in Pydantic classes
- No business logic methods
- Keep models pure data containers
- Methods only for serialization/deserialization helpers