---
title: "Documentation Standards"
description: "Pyramid Principle, docstrings, and logical symbols for clear documentation"
globs: ["**/*.py", "**/*.md"]
---

## Pyramid Principle for Docstrings

Every script must have a docstring following the Pyramid Principle:

```python
def process_data(input: DataNode) -> ResultNode:
    """proc-001 - Transform raw data into validated results
    
    1. Validate input constraints
    2. Apply business transformations  
    3. Generate output with metadata
    
    Why: Central processing ensures consistent validation
    How: Pipeline pattern with type-safe transforms
    
    Requirements:
    ⏳ Add retry logic for external calls
    🟠 Implement caching layer
    ☑️ Basic transformation logic
    ✅ Input validation (verified by AF)
    
    Control Flow:
    input → validate → transform → enrich → output
    """
```

### Structure Requirements

1. **Oner** (One-liner)
   - ID prefix (e.g., proc-001)
   - Single line describing WHAT and WHY
   - Elevator pitch focus

2. **Pyramid Points** (3-4 bullets)
   - Main steps or components
   - Focus on WHY over HOW
   - Each point should stand alone

3. **Supporting Evidence**
   - Expand pyramid points
   - Add context and justification
   - Keep it concise

4. **Requirements Tracking**
   - ⏳ Backlog/planned features
   - 🟠 In progress
   - ☑️ Implemented
   - ✅ Verified by human (never written by AI)

5. **Control Flow**
   - ASCII diagram showing data flow
   - Vertically compressed
   - Use arrows: →, ↓, ↗

## Logical Symbols Reference

### Basic Connectives
- `∧` - AND: "Fast ∧ readable code"
- `∨` - OR: "Use pandas ∨ numpy"  
- `¬` - NOT: "¬ include deprecated functions"
- `→` - IF/THEN: "Invalid input → error message"
- `↔` - IFF: "Use async ↔ handling I/O"

### Quantifiers
- `∀` - FOR ALL: "∀ functions have docstrings"
- `∃` - EXISTS: "∃ at least one test per function"
- `∃!` - EXACTLY ONE: "∃! main() function"

### Inference
- `∴` - THEREFORE: "Tests fail ∴ needs debugging"
- `∵` - BECAUSE: "List comprehension ∵ more Pythonic"
- `⊢` - PROVES: "Type hints ⊢ better documentation"
- `⊨` - SATISFIES: "Implementation ⊨ requirements"

### Architecture Relations
- `→` - DEPENDS ON: "Flask → SQLAlchemy"
- `⇒` - LEADS TO: "High traffic ⇒ implement caching"
- `⟶` - FLOWS TO: "Input ⟶ validation ⟶ processing"
- `⊃` - CONTAINS: "Django ⊃ multiple apps"
- `≺` - PRECEDES: "Auth ≺ authorization"

## Function Documentation

### Numbered Steps Pattern
```python
def complex_operation(data: Input) -> Output:
    """op-002 - Multi-step data processing
    
    1. Validate preconditions
    2. Transform data
    3. Post-process results
    """
    # 1. Validate preconditions
    assert data.is_valid(), "Invalid input data"
    
    # 2. Transform data  
    transformed = apply_rules(data)  # using rules from config
    
    # 3. Post-process results
    return Output(
        result=transformed,
        metadata=generate_metadata()  # adds timestamp, version
    )
```

## Comments Best Practices

### EOL Comments - Explain WHY
```python
tokens = len(text.split())  # rough estimate, replace with tiktoken
result = process(data)      # must happen before validation
cache[key] = value         # TTL handled by cache layer
```

### Full-line Comments - Mark Steps
```python
# Step 1: Validate all inputs before processing
validate_inputs(data)

# Step 2: Apply transformation pipeline
result = transform(data)
```

## Prompt Documentation

### Implicit Concatenation for Clarity
```python
prompt = (
    "System: You are a helpful assistant\n"  # base instruction
    f"Context: {context}\n"                   # dynamic context
    "Task: Analyze the following data\n"      # specific task
    f"Data: {data}"                          # user input
)
```

## #CLAUDE.md Files

Every importable package must have #CLAUDE.md with:
- 8-10 line description of purpose
- Boundaries and responsibilities  
- Extension checklist
- Link to flow-chart section
- Usage examples if applicable