---
title: "Core Principles - Always Applied"
description: "Fundamental architectural principles that must always be followed"
alwaysApply: true
---

## FCIS - Functional Core, Imperative Shell

- Keep business logic pure and functional
- Side effects only at the boundaries (main(), imperative shell)
- Functional core processes data, imperative shell handles I/O
- Maximum two levels deep from main() for helper functions

## Brittle Minimalistic Code

- NO exception handling outside main() - let errors bubble up immediately
- Write code like blog tutorials - simple, explanatory, breaks immediately
- Assert liberally - fail fast, fail loud
- No defensive programming - assume happy path, break on edge cases
- If agents need exception handling for debugging, use .temp files

## Business Logic is Sacred

- Business logic readability is PARAMOUNT
- Every main() should clearly show the pipeline/flow
- Abstract only when function name + EOL comment is self-explanatory
- If abstraction obscures understanding, keep it inline
- Protect clarity of core business logic at ALL costs

## Duplo Blocks Style

- Strict style definition for maximum code similarity
- Consistent patterns across all modules
- Code should look like it was written by one person
- Follow established patterns religiously

## No Logging Libraries

- Use rich prints with color via display utils
- Let exceptions halt the run (brittle)
- Trace backs are better than warning logs
- LLMs never miss a traceback but often miss logs

## Code Justification

- Add EOL comments explaining WHY not WHAT
- Explain why this approach vs alternatives
- Note what NEEDS this code (dependencies)
- Enable ruthless culling through clear justification

## Sacred Principles Summary

1. FCIS architecture - functional core, imperative shell
2. Brittle code that breaks immediately on issues  
3. Business logic clarity above all else
4. No exception handling in business logic
5. Fail fast with asserts and let errors bubble
6. Self-documenting through clear naming and structure
7. Abstract only when it improves understanding