---
description: Standard exclude patterns for all linters
---
# Lin<PERSON> Exclude Patterns

All linters should exclude these paths. See [pyproject.toml](mdc:pyproject.toml) for the canonical list.

## Directories to Exclude
```
# Core exclusions
.archive/      # Archive directory
.venv/         # Virtual environment
build/         # Build directories
dist/          # Distribution files
__pycache__/   # Python cache
notebooks/     # Jupyter notebooks
.tmp/          # Temporary files directory
docs/          # Documentation directory

# Cache directories
.mypy_cache/   # Mypy type checker cache
.sourcery/     # Sourcery analysis cache
.ruff_cache/   # Ruff cache
.pytest_cache/ # Pytest cache
.coverage/     # Coverage reports
htmlcov/       # HTML coverage reports
.hypothesis/   # Hypothesis testing cache
.tox/          # Tox testing environments
.nox/          # Nox testing environments

# IDE directories
.cursor/       # Cursor IDE configuration
.vscode/       # VS Code configuration
.idea/         # JetBrains IDE configuration

# Build outputs
site/          # MkDocs build output
_build/        # Sphinx build output
node_modules/  # Node.js dependencies
```

## File Patterns to Exclude
```
*.egg-info     # Egg info directories
*.pyc          # Python compiled files
*.pyo          # Python optimized files
*.pyd          # Python extension modules
*.so           # Shared object files
*.dll          # Dynamic link libraries
*.log          # Log files
*.lock         # Lock files (uv.lock, etc.)
*.orig         # Merge conflict originals
*.rej          # Patch rejection files
*.bak          # Backup files
*.swp          # Vim swap files
*.swo          # Vim swap overflow
*~             # Editor backup files
.DS_Store      # macOS metadata
Thumbs.db      # Windows thumbnail cache
.env           # Environment files
.env.*         # Environment variant files
justfile       # Just command runner
Makefile       # Make files
*.mk           # Make include files
```