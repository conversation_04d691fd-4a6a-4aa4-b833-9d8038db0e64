---
alwaysApply: true
---
# Linting Philosophy

## Core Principle: Brittle Code
We follow a "brittle minimalistic code" philosophy - code should fail fast and loud rather than hiding problems.

## Ruff Configuration
See pyproject.toml for full configuration.

### Auto-fix These (Safe)
- W291, W293 - Whitespace issues
- F401, F841 - Unused imports/variables  
- UP035 - Type hint updates
- RET504 - Unnecessary assignments
- C417, C403 - Comprehension improvements
- PERF401 - Performance improvements

### Purposefully Ignored
- S101 - Assert statements (we want brittle code)
- BLE001 - Blind exceptions (fix manually)
- T201 - Print statements (we use rich prints)
- E402 - Import order in notebooks
- All D rules - Docstrings (we handle differently)

### Manual Review Required
- PLR2004 - Magic numbers (should be constants)
- DTZ005 - Timezone-aware datetime
- S603/S607 - Security issues with subprocess
- SLF001 - Private member access
