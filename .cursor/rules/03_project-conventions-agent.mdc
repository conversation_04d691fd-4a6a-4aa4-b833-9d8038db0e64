# Project Conventions

## VIRTUAL ENVIRONMENT CONVENTIONS

-   Use `.venv` instead of `venv` for virtual environments.
-   Use astral uv: `uv add`, `uv sync`, `uv run` instead of pip.
-   Always run scripts straight after fix to ensure you have actually fixed it.

## SECURITY AND CONFIGURATION

-   Always put keys etc in `.env` file and access via dotenv.
-   Config params should be stored globally either in CFG singleton in `run.py` or `run_config.py`.
-   All centralized config/params should live in `.env` and either call directly in each file using `os.getenv` or use a `_get_*` function declared in file if an object is required.

## REPO DOCUMENTATION

-   Maintain a comprehensive spec section that serves as a complete instruction set for rebuilding the repo from scratch.
-   Update `claude.md` incrementally to track design choices and repository structure.
-   Ensure the spec can be used as a standalone guide for repository reconstruction.

## FILE NAMING CONVENTIONS

-   If file isn't working it should have `**_WIP.**` suffix, changed back once confirmed working and tests working.
-   Use `aa_cat_filename` pattern with slack for file organization:
    -   `aa_` prefix for ordering (00-99)
    -   `cat_` for category grouping
    -   `filename` for descriptive name
    -   Example: `10_data_processor.py`, `20_api_handler.py`, `30_utils_helpers.py`
-   Allow slack in numbering for future insertions (e.g., 10, 20, 30 instead of 01, 02, 03)

## PROJECT STRUCTURE AND SCRIPT CONVENTIONS

-   Use `./src/appname` and `./tests`, `./.venv`, `./.claude`, `./CLAUDE.md`, `./scripts` and `./run.py` file structure.
-   Launch scripts should be named `run_***.sh`.
-   Include a justfile in root to provide a minimalistic cheatsheet for running scripts, main app, tests, setup etc. for other developers.
-   Follow duplo-alphabetised structure:
    -   Primary sort by numeric prefix (00-99)
    -   Secondary sort by category
    -   Tertiary sort by filename
    -   Allows logical grouping while maintaining clear order

## RUN SCRIPT CONVENTIONS

-   Create `run.sh` with a standard template that includes shebang, error handling, and executable permissions.
-   Ensure `run.sh` can be used to launch the main application, run tests, or perform setup tasks.

## MODULE INITIALIZATION

-   Add blank `__init__.py` to all app dirs, `/scripts` etc to make them modules. If using uv for deps, register modules in `pyproject.toml`.

## JUSTFILE DOCUMENTATION

-   Provide a concise, one-line description of the justfile's purpose and ideal formatting to serve as a quick reference for developers.

## SCRIPT BOILERPLATE REQUIREMENTS

-   All Python scripts must include main protection:
```python
if __name__ == "__main__":
    main()
```
-   Standard script structure:
```python
#!/usr/bin/env python3
"""Brief description of what the script does."""

# imports...

def main():
    """Main entry point."""
    # implementation...

if __name__ == "__main__":
    main()
```

## COMPILE REQUIREMENTS

-   Compile requirements script location: `./scripts/compile_requirements.py`
-   Used to generate consolidated requirements from pyproject.toml or other sources
-   Run after dependency changes to update requirements.txt

## TEMPORARY FILES

-   If you HAVE to use them, don't create temporary 'check files' in root, put them in `./.tmp` and prefix those files `.tmp_*`. Only do it when really necessary.
description:
globs:
alwaysApply: false
---
