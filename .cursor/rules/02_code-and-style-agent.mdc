---
description:
globs:
alwaysApply: false
---

# Code & Style Conventions

## CODE CLARITY AND READABILITY

-   Explicitly state `elif` when multiple states, don't leave reader guessing. `if x: abc elif y: xyz`. Not just `else xyz`.
-   Always reflect on how to solve problems with least code, most elegantly, DRY, very clearly named functions and again, with least code. Only strictly necessary EH etc. Use `_helper` functions with clearly `#-----` divisioned groupings. You can also use static classes with <6 char names for in-script grouping as well.
-   Use typehints.
-   Use in-script dataclasses where appropriate.

## IMPORT AND MODULE MANAGEMENT

-   Never use the `os.path` hack to fix import issues. Do it properly, check `pyproject.toml` or module structure.
-   If module import issues, solve via fixing `pyproject.toml` / dir structure / import method etc, don't solve via bash workaround or by adding mid-import `sys.path` fixes, it's gross.

## TABLE FORMATTING

-   Always include rich table divider lines.

## LOGGING CONFIGURATION

```python
# add to src/name/config.py
from loguru import logger as log
from dataclasses import dataclass
import sys

@dataclass
class Config:
    """Configuration for the application."""
    # LOG_LEVEL: str = "DEBUG"   # quick toggle
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
    
    def log_init(self):
        """Initialize loguru with color formatting."""
        log.remove()  # Remove default handler
        log.add(sys.stderr, format=self.LOG_FORMAT, level=self.LOG_LEVEL)

# add to main.py at top:
# CONFIG = Config()
# CONFIG.log_init()
```

## DISPLAY LOGIC PATTERNS

-   Use rich prints with colors for better visibility and debugging
-   Example pattern:
```python
from rich import print

# Use colors to distinguish different types of output
print("[green]Success:[/green] Operation completed")
print("[yellow]Warning:[/yellow] Check configuration")
print("[red]Error:[/red] Failed to process")
print("[cyan]Info:[/cyan] Processing file...")
print("[magenta]Debug:[/magenta] Variable state: ...")
```

## HELPER FUNCTION RULES

-   Max 2 levels deep for helper functions
-   Use self-explanatory names that describe what the function does
-   Group related helpers with clear `#-----` divisions
-   Example:
```python
# ----- File Processing Helpers -----
def _validate_file_path(path: str) -> bool:
    """Check if file path is valid and accessible."""
    ...

def _read_json_safely(path: str) -> dict:
    """Read JSON file with basic error handling."""
    ...

# ----- Data Transform Helpers -----
def _normalize_text(text: str) -> str:
    """Normalize text for consistent processing."""
    ...
```

## HANDLERS VS PROCESSORS

-   **Handlers**: Respond to events, manage state changes, coordinate operations
    -   Named: `handle_*` (e.g., `handle_user_input`, `handle_error`)
    -   Focus on orchestration and flow control
-   **Processors**: Transform data, perform calculations, execute business logic
    -   Named: `process_*` (e.g., `process_data`, `process_request`)
    -   Focus on data transformation and computation

## MODULE EXPORTS

-   Always define `__all__` in modules to explicitly control what gets exported
-   Place at the top of the file after imports
-   Example:
```python
__all__ = [
    "public_function",
    "PublicClass",
    "CONSTANT",
]
```

## PARSING AND DATA EXTRACTION

-   I very rarely use programmatic parsers. Almost always we will use LLMs w structured outputs for this.
