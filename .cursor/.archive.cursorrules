# Cursor Rules for PFC Project

## PURPOSEFUL BRITTLENESS DURING LLM DEV

**CRITICAL:** This is the most important rule set. Always follow these principles:

- **NO defaults** - Let things break if incorrect, we'll handle it later
- **NO exception handling** - Don't catch exceptions unless absolutely necessary
- **NO API mocking** - Use real APIs, let them fail if they're down
- **NO fallbacks** - Don't provide backup values or alternative flows
- **NO defensive programming** - Let the system fail fast and clearly

Example: Instead of `default_factory=lambda self: self.ss_list[0].timestamp if self.ss_list else ""`, use `default_factory=lambda self: self.ss_list[0].timestamp` - let it break if `self.ss_list` is empty.

**CORE RULE:** Always keep going (full-auto) pursuing task completion + running the finished script until the script is working as expected.

**CORE LOOP:** 
1. WRITE CODE 
2. REFLECT 
3. FIX IF REQUIRED (<-2 or 3) 
4. Run Script, check logs and output meet expectations 
5. Debug/fix loop 
6. Restart at (1) if task requirements are not met

ALWAYS run the script to confirm your changes didn't have surprise errors. ALWAYS keep going if task requirements are not met.

## VIRTUAL ENVIRONMENT CONVENTIONS

- Use `.venv` instead of `venv` for virtual environments
- Use astral uv: `uv add`, `uv sync`, `uv run` instead of pip
- Always run scripts straight after fix to ensure you have actually fixed it

## SECURITY AND CONFIGURATION

- Always put keys etc in `.env` file and access via dotenv
- Config params should be stored globally either in CFG singleton in `run.py` or `run_config.py`
- All centralized config/params should live in `.env` and either call directly in each file using `os.getenv` or use a `_get_*` function declared in file if an object is required

## DEVELOPMENT WORKFLOW

- Commit regularly during operations for easy save points `<feat>` `<fix>` tags etc. Use sub-tasks (parallel) for this to prevent blocking the main claude code thread
- Always update `claude.md` using subtasks to not block the main claude code thread
- Always run scripts and existing tests to prevent regression errors
- After completing tasks, suggest short list of regression tests to add to `./tests` to prevent regression errors going forward. Each suggestion should be a single line "if [function] does/doesn't x then broken" for readability ease. When writing those tests, those single line descriptions should be added to that tests success/fail print
- Always run scripts after editing to check they work
- Use zsh as your terminal

## REPO DOCUMENTATION

- Maintain a comprehensive spec section that serves as a complete instruction set for rebuilding the repo from scratch
- Update `claude.md` incrementally to track design choices and repository structure
- Ensure the spec can be used as a standalone guide for repository reconstruction

## FILE NAMING CONVENTIONS

- If file isn't working it should have `**_WIP.**` suffix, changed back once confirmed working and tests working

## PROJECT STRUCTURE AND SCRIPT CONVENTIONS

- Use `./src/appname` and `./tests`, `./.venv`, `./.claude`, `./CLAUDE.md`, `./scripts` and `./run.py` file structure
- Launch scripts should be named `run_***.sh`
- Include a justfile in root to provide a minimalistic cheatsheet for running scripts, main app, tests, setup etc. for other developers

## CODE REFERENCE AND RESEARCH

- Always check mcp context7 for code snippets for APIs if available (use minimal keywords, try several things in parallel as it filters quite aggressively)
- Always check online for working code snippets to avoid hallucinations

## RUN SCRIPT CONVENTIONS

- Create `run.sh` with a standard template that includes shebang, error handling, and executable permissions
- Ensure `run.sh` can be used to launch the main application, run tests, or perform setup tasks

## MODULE INITIALIZATION

- Add blank `__init__.py` to all app dirs, `/scripts` etc to make them modules. If using uv for deps, register modules in `pyproject.toml`

## TASK MANAGEMENT AND DEVELOPMENT

- Always explicitly plan what you can do as sub-task agents to maximize speed and parallelization of your actions

## IMPORT AND MODULE MANAGEMENT

- Never use the `os.path` hack to fix import issues. Do it properly, check `pyproject.toml` or module structure
- If module import issues, solve via fixing `pyproject.toml` / dir structure / import method etc, don't solve via bash workaround or by adding mid-import `sys.path` fixes, it's gross

## CODE CLARITY AND READABILITY

- Explicitly state `elif` when multiple states, don't leave reader guessing. `if x: abc elif y: xyz`. Not just `else xyz`
- Always reflect on how to solve problems with least code, most elegantly, DRY, very clearly named functions and again, with least code. Only strictly necessary EH etc. Use `_helper` functions with clearly `#-----` divisioned groupings. You can also use static classes with <6 char names for in-script grouping as well
- Use typehints
- Use in-script dataclasses where appropriate

## GIT COMMIT CONVENTIONS

- Please sign git commit messages `-Claude` for clarity

## JUSTFILE DOCUMENTATION

- Provide a concise, one-line description of the justfile's purpose and ideal formatting to serve as a quick reference for developers

## TABLE FORMATTING

- Always include rich table divider lines

## TASK TRACKING

- Maintain a `zTasks.md` file as a visual tracker for tasks
- Task checklist should be at the top of `zTasks.md`, in chronological order
- New tasks go at the bottom of the checklist
- Complete tasks should be checked off but not removed
- Include verbatim copies of tasks and requirements
- Organize the rest of the file by feature/branch for readability
- Indent sub-tasks to show hierarchy
- Macro tasks should be markdown hyperlinked to the original prompt
- `zTasks.md` should be in the root directory and gitignored

## TASK LIST MANAGEMENT

- Don't mock anything not explicitly told to mock
- Maintain a list of tasks at the end of the task list
- Prepend personal tasks with `af_**` prefix
- If a task has a dependency on the user, move it after the relevant "af_" task
- Pause and wait for user input if encountering a task with user dependency

## PERSONAL DETAILS

- 'af' is my name. Alex Foster.

## DEBUGGING AND TESTING

- How do you know it's working? Add minimal test cases that print or log core functionality to verify basic system behavior
- **ALWAYS RUN AND TEST YOUR CODE BEFORE COMPLETING.** How else do you know it works?
- Diagnose issues without mocking outputs. Never, ever, mock outputs. The system should work as expected, and otherwise fail in almost all cases
- Similarly, almost never use fallback values
- All values that may change should be in `main.py` as class CONFIG glob vars or `.env` so that a reader is entirely aware of all important decisions made

## CLI TOOLS

- `cc` = claudecode

## PARSING AND DATA EXTRACTION

- I very rarely use programmatic parsers. Almost always we will use LLMs w structured outputs for this.

## TEMPORARY FILES

- If you HAVE to use them, don't create temporary 'check files' in root, put them in `./.tmp` and prefix those files `.tmp_*`. Only do it when really necessary.

## LOGGING CONFIGURATION

```python
# add to src/name/config.py
from loguru import logger as log

@dataclass
class Config:
    """Configuration for the application."""
    # LOG_LEVEL: str = "DEBUG"   # quick toggle
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
    
    def log_init(self):
        """Initialize loguru with color formatting."""
        log.remove()  # Remove default handler
        log.add(sys.stderr, format=self.LOG_FORMAT, level=self.LOG_LEVEL)

# add to main.py at top:
CONFIG.log_init()
```

## REFACTORING RULE

Once termination condition met and stopping a chain of interactions, do one final task: Think through how to refactor the script. Suggest the refactor and ask for feedback. 